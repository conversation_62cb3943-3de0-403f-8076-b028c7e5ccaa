// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User roles enum
enum UserRole {
  ADMIN
  BASE_COMMANDER
  LOGISTICS_OFFICER
}

// Transaction types enum
enum TransactionType {
  PURCHASE
  TRANSFER_IN
  TRANSFER_OUT
  ASSIGNMENT
  EXPENDITURE
  RETURN
  MAINTENANCE
}

// Transfer status enum
enum TransferStatus {
  PENDING
  APPROVED
  IN_TRANSIT
  COMPLETED
  CANCELLED
}

// Asset status enum
enum AssetStatus {
  AVAILABLE
  ASSIGNED
  IN_MAINTENANCE
  DECOMMISSIONED
  LOST
}

// Equipment categories
enum EquipmentCategory {
  WEAPONS
  VEHICLES
  AMMUNITION
  COMMUNICATION
  MEDICAL
  SUPPLIES
  ELECTRONICS
  PROTECTIVE_GEAR
}

// Users table - Authentication and role management
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  firstName   String
  lastName    String
  password    String
  role        UserRole
  baseId      String?
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  base                Base?                @relation(fields: [baseId], references: [id])
  createdTransactions Transaction[]        @relation("CreatedBy")
  approvedTransactions Transaction[]       @relation("ApprovedBy")
  assignments         AssetAssignment[]
  auditLogs           AuditLog[]

  @@map("users")
}

// Military bases
model Base {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  location    String
  commanderId String?  @unique
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users               User[]
  commander           User?                @relation(fields: [commanderId], references: [id])
  inventory           Inventory[]
  fromTransfers       Transfer[]           @relation("FromBase")
  toTransfers         Transfer[]           @relation("ToBase")
  purchases           Purchase[]
  assignments         AssetAssignment[]

  @@map("bases")
}

// Asset definitions
model Asset {
  id          String            @id @default(cuid())
  name        String
  code        String            @unique
  category    EquipmentCategory
  description String?
  unitPrice   Decimal?          @db.Decimal(10, 2)
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  inventory     Inventory[]
  transactions  Transaction[]
  assignments   AssetAssignment[]

  @@map("assets")
}

// Current inventory levels per base
model Inventory {
  id              String   @id @default(cuid())
  baseId          String
  assetId         String
  openingBalance  Int      @default(0)
  currentBalance  Int      @default(0)
  assignedCount   Int      @default(0)
  availableCount  Int      @default(0)
  lastUpdated     DateTime @default(now())

  // Relations
  base  Base  @relation(fields: [baseId], references: [id])
  asset Asset @relation(fields: [assetId], references: [id])

  @@unique([baseId, assetId])
  @@map("inventory")
}

// All asset transactions
model Transaction {
  id            String          @id @default(cuid())
  type          TransactionType
  baseId        String
  assetId       String
  quantity      Int
  unitPrice     Decimal?        @db.Decimal(10, 2)
  totalValue    Decimal?        @db.Decimal(10, 2)
  description   String?
  referenceId   String?         // Links to Purchase, Transfer, etc.
  createdById   String
  approvedById  String?
  createdAt     DateTime        @default(now())
  approvedAt    DateTime?

  // Relations
  asset     Asset @relation(fields: [assetId], references: [id])
  createdBy User  @relation("CreatedBy", fields: [createdById], references: [id])
  approvedBy User? @relation("ApprovedBy", fields: [approvedById], references: [id])

  @@map("transactions")
}

// Purchase records
model Purchase {
  id          String   @id @default(cuid())
  baseId      String
  vendor      String
  totalAmount Decimal  @db.Decimal(10, 2)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  base  Base            @relation(fields: [baseId], references: [id])
  items PurchaseItem[]

  @@map("purchases")
}

// Purchase line items
model PurchaseItem {
  id         String  @id @default(cuid())
  purchaseId String
  assetId    String
  quantity   Int
  unitPrice  Decimal @db.Decimal(10, 2)
  totalPrice Decimal @db.Decimal(10, 2)

  // Relations
  purchase Purchase @relation(fields: [purchaseId], references: [id])
  asset    Asset    @relation(fields: [assetId], references: [id])

  @@map("purchase_items")
}

// Asset transfers between bases
model Transfer {
  id            String         @id @default(cuid())
  fromBaseId    String
  toBaseId      String
  status        TransferStatus @default(PENDING)
  requestedById String
  approvedById  String?
  description   String?
  requestedAt   DateTime       @default(now())
  approvedAt    DateTime?
  completedAt   DateTime?

  // Relations
  fromBase    Base           @relation("FromBase", fields: [fromBaseId], references: [id])
  toBase      Base           @relation("ToBase", fields: [toBaseId], references: [id])
  items       TransferItem[]

  @@map("transfers")
}

// Transfer line items
model TransferItem {
  id         String @id @default(cuid())
  transferId String
  assetId    String
  quantity   Int

  // Relations
  transfer Transfer @relation(fields: [transferId], references: [id])
  asset    Asset    @relation(fields: [assetId], references: [id])

  @@map("transfer_items")
}

// Asset assignments to personnel
model AssetAssignment {
  id          String      @id @default(cuid())
  baseId      String
  assetId     String
  assignedTo  String      // Personnel name/ID
  quantity    Int
  status      AssetStatus @default(ASSIGNED)
  assignedAt  DateTime    @default(now())
  returnedAt  DateTime?
  description String?

  // Relations
  base  Base  @relation(fields: [baseId], references: [id])
  asset Asset @relation(fields: [assetId], references: [id])
  user  User  @relation(fields: [assignedTo], references: [id])

  @@map("asset_assignments")
}

// Comprehensive audit logging
model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  resource  String
  resourceId String?
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
