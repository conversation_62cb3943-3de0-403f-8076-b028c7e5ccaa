// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Note: SQLite doesn't support enums, so we'll use String fields with validation in the application

// Users table - Authentication and role management
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  username    String   @unique
  firstName   String
  lastName    String
  password    String
  role        String   // ADMIN, BASE_COMMANDER, LOGISTICS_OFFICER
  baseId      String?
  isActive    Boolean  @default(true)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  base                Base?                @relation("BaseUsers", fields: [baseId], references: [id])
  commandedBases      Base[]               @relation("BaseCommander")
  createdTransactions Transaction[]        @relation("CreatedBy")
  approvedTransactions Transaction[]       @relation("ApprovedBy")
  purchases           Purchase[]
  assignments         AssetAssignment[]
  auditLogs           AuditLog[]

  @@map("users")
}

// Military bases
model Base {
  id          String   @id @default(cuid())
  name        String   @unique
  code        String   @unique
  location    String
  commanderId String?  @unique
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users               User[]               @relation("BaseUsers")
  commander           User?                @relation("BaseCommander", fields: [commanderId], references: [id])
  inventory           Inventory[]
  fromTransfers       Transfer[]           @relation("FromBase")
  toTransfers         Transfer[]           @relation("ToBase")
  purchases           Purchase[]
  assignments         AssetAssignment[]

  @@map("bases")
}

// Asset definitions
model Asset {
  id          String            @id @default(cuid())
  name        String
  code        String            @unique
  category    String            // WEAPONS, VEHICLES, AMMUNITION, etc.
  description String?
  unitPrice   Float?
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  inventory     Inventory[]
  transactions  Transaction[]
  assignments   AssetAssignment[]
  purchaseItems PurchaseItem[]
  transferItems TransferItem[]

  @@map("assets")
}

// Current inventory levels per base
model Inventory {
  id              String   @id @default(cuid())
  baseId          String
  assetId         String
  openingBalance  Int      @default(0)
  currentBalance  Int      @default(0)
  assignedCount   Int      @default(0)
  availableCount  Int      @default(0)
  lastUpdated     DateTime @default(now())

  // Relations
  base  Base  @relation(fields: [baseId], references: [id])
  asset Asset @relation(fields: [assetId], references: [id])

  @@unique([baseId, assetId])
  @@map("inventory")
}

// All asset transactions
model Transaction {
  id            String          @id @default(cuid())
  type          String          // PURCHASE, TRANSFER_IN, TRANSFER_OUT, etc.
  baseId        String
  assetId       String
  quantity      Int
  unitPrice     Float?
  totalValue    Float?
  description   String?
  referenceId   String?         // Links to Purchase, Transfer, etc.
  createdById   String
  approvedById  String?
  createdAt     DateTime        @default(now())
  approvedAt    DateTime?

  // Relations
  asset     Asset @relation(fields: [assetId], references: [id])
  createdBy User  @relation("CreatedBy", fields: [createdById], references: [id])
  approvedBy User? @relation("ApprovedBy", fields: [approvedById], references: [id])

  @@map("transactions")
}

// Purchase records
model Purchase {
  id          String   @id @default(cuid())
  baseId      String
  vendor      String
  totalAmount Float
  description String?
  createdById String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  base      Base            @relation(fields: [baseId], references: [id])
  createdBy User            @relation(fields: [createdById], references: [id])
  items     PurchaseItem[]

  @@map("purchases")
}

// Purchase line items
model PurchaseItem {
  id         String  @id @default(cuid())
  purchaseId String
  assetId    String
  quantity   Int
  unitPrice  Float
  totalPrice Float

  // Relations
  purchase Purchase @relation(fields: [purchaseId], references: [id])
  asset    Asset    @relation(fields: [assetId], references: [id])

  @@map("purchase_items")
}

// Asset transfers between bases
model Transfer {
  id            String         @id @default(cuid())
  fromBaseId    String
  toBaseId      String
  status        String         @default("PENDING") // PENDING, APPROVED, IN_TRANSIT, COMPLETED, CANCELLED
  requestedById String
  approvedById  String?
  description   String?
  requestedAt   DateTime       @default(now())
  approvedAt    DateTime?
  completedAt   DateTime?

  // Relations
  fromBase    Base           @relation("FromBase", fields: [fromBaseId], references: [id])
  toBase      Base           @relation("ToBase", fields: [toBaseId], references: [id])
  items       TransferItem[]

  @@map("transfers")
}

// Transfer line items
model TransferItem {
  id         String @id @default(cuid())
  transferId String
  assetId    String
  quantity   Int

  // Relations
  transfer Transfer @relation(fields: [transferId], references: [id])
  asset    Asset    @relation(fields: [assetId], references: [id])

  @@map("transfer_items")
}

// Asset assignments to personnel
model AssetAssignment {
  id          String      @id @default(cuid())
  baseId      String
  assetId     String
  assignedTo  String      // Personnel name/ID
  quantity    Int
  status      String      @default("ASSIGNED") // AVAILABLE, ASSIGNED, IN_MAINTENANCE, DECOMMISSIONED, LOST
  assignedAt  DateTime    @default(now())
  returnedAt  DateTime?
  description String?

  // Relations
  base  Base  @relation(fields: [baseId], references: [id])
  asset Asset @relation(fields: [assetId], references: [id])
  user  User  @relation(fields: [assignedTo], references: [id])

  @@map("asset_assignments")
}

// Comprehensive audit logging
model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  resource  String
  resourceId String?
  oldValues String? // JSON string
  newValues String? // JSON string
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
