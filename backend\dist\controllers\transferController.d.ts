import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        baseId: string;
    };
}
export declare const transferController: {
    getAll(req: AuthenticatedRequest, res: Response): Promise<void>;
    getById(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    create(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    updateStatus(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    delete(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
};
export {};
//# sourceMappingURL=transferController.d.ts.map