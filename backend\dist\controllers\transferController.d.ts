import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        baseId: string;
    };
}
export declare const transferController: {
    getAll(req: AuthenticatedRequest, res: Response): Promise<void>;
    getById(req: AuthenticatedRequest, res: Response): Promise<void>;
    create(req: AuthenticatedRequest, res: Response): Promise<void>;
    updateStatus(req: AuthenticatedRequest, res: Response): Promise<void>;
    delete(req: AuthenticatedRequest, res: Response): Promise<void>;
};
export {};
//# sourceMappingURL=transferController.d.ts.map