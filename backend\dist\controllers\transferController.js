"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transferController = void 0;
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const prisma = new client_1.PrismaClient();
exports.transferController = {
    async getAll(req, res) {
        try {
            const { page = 1, limit = 10, baseId, status, startDate, endDate, } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const user = req.user;
            let whereClause = {};
            if (user.role === enums_1.UserRole.BASE_COMMANDER || user.role === enums_1.UserRole.LOGISTICS_OFFICER) {
                whereClause.OR = [
                    { fromBaseId: user.baseId },
                    { toBaseId: user.baseId },
                ];
            }
            if (baseId) {
                whereClause.OR = [
                    { fromBaseId: baseId },
                    { toBaseId: baseId },
                ];
            }
            if (status) {
                whereClause.status = status;
            }
            if (startDate || endDate) {
                whereClause.requestedAt = {};
                if (startDate) {
                    whereClause.requestedAt.gte = new Date(startDate);
                }
                if (endDate) {
                    whereClause.requestedAt.lte = new Date(endDate);
                }
            }
            const [transfers, total] = await Promise.all([
                prisma.transfer.findMany({
                    where: whereClause,
                    skip,
                    take: Number(limit),
                    orderBy: { requestedAt: 'desc' },
                    include: {
                        fromBase: {
                            select: { id: true, name: true, code: true },
                        },
                        toBase: {
                            select: { id: true, name: true, code: true },
                        },
                        items: {
                            include: {
                                asset: {
                                    select: { id: true, name: true, code: true, category: true },
                                },
                            },
                        },
                    },
                }),
                prisma.transfer.count({ where: whereClause }),
            ]);
            const pages = Math.ceil(total / Number(limit));
            res.json({
                transfers,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages,
                },
            });
        }
        catch (error) {
            console.error('Error fetching transfers:', error);
            res.status(500).json({ error: 'Failed to fetch transfers' });
        }
    },
    async getById(req, res) {
        try {
            const { id } = req.params;
            const user = req.user;
            const transfer = await prisma.transfer.findUnique({
                where: { id },
                include: {
                    fromBase: {
                        select: { id: true, name: true, code: true, location: true },
                    },
                    toBase: {
                        select: { id: true, name: true, code: true, location: true },
                    },
                    items: {
                        include: {
                            asset: {
                                select: { id: true, name: true, code: true, category: true, unitPrice: true },
                            },
                        },
                    },
                },
            });
            if (!transfer) {
                return res.status(404).json({ error: 'Transfer not found' });
            }
            if (user.role !== enums_1.UserRole.ADMIN) {
                const hasAccess = transfer.fromBaseId === user.baseId || transfer.toBaseId === user.baseId;
                if (!hasAccess) {
                    return res.status(403).json({ error: 'Access denied' });
                }
            }
            res.json(transfer);
        }
        catch (error) {
            console.error('Error fetching transfer:', error);
            res.status(500).json({ error: 'Failed to fetch transfer' });
        }
    },
    async create(req, res) {
        try {
            const { fromBaseId, toBaseId, description, items } = req.body;
            const user = req.user;
            if (user.role !== enums_1.UserRole.ADMIN && user.baseId !== fromBaseId) {
                return res.status(403).json({ error: 'Can only create transfers from your assigned base' });
            }
            for (const item of items) {
                const inventory = await prisma.inventory.findUnique({
                    where: {
                        baseId_assetId: {
                            baseId: fromBaseId,
                            assetId: item.assetId,
                        },
                    },
                });
                if (!inventory || inventory.availableCount < item.quantity) {
                    const asset = await prisma.asset.findUnique({
                        where: { id: item.assetId },
                        select: { name: true },
                    });
                    return res.status(400).json({
                        error: `Insufficient inventory for ${asset?.name || 'asset'}. Available: ${inventory?.availableCount || 0}, Requested: ${item.quantity}`,
                    });
                }
            }
            const transfer = await prisma.$transaction(async (tx) => {
                const newTransfer = await tx.transfer.create({
                    data: {
                        fromBaseId,
                        toBaseId,
                        description,
                        requestedById: user.id,
                        status: enums_1.TransferStatus.PENDING,
                    },
                });
                const transferItems = await Promise.all(items.map((item) => tx.transferItem.create({
                    data: {
                        transferId: newTransfer.id,
                        assetId: item.assetId,
                        quantity: item.quantity,
                    },
                })));
                for (const item of items) {
                    await tx.inventory.update({
                        where: {
                            baseId_assetId: {
                                baseId: fromBaseId,
                                assetId: item.assetId,
                            },
                        },
                        data: {
                            availableCount: {
                                decrement: item.quantity,
                            },
                        },
                    });
                }
                return newTransfer;
            });
            const completeTransfer = await prisma.transfer.findUnique({
                where: { id: transfer.id },
                include: {
                    fromBase: {
                        select: { id: true, name: true, code: true },
                    },
                    toBase: {
                        select: { id: true, name: true, code: true },
                    },
                    items: {
                        include: {
                            asset: {
                                select: { id: true, name: true, code: true, category: true },
                            },
                        },
                    },
                },
            });
            res.status(201).json(completeTransfer);
        }
        catch (error) {
            console.error('Error creating transfer:', error);
            res.status(500).json({ error: 'Failed to create transfer' });
        }
    },
    async updateStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            const user = req.user;
            const transfer = await prisma.transfer.findUnique({
                where: { id },
                include: {
                    fromBase: {
                        select: { id: true, name: true },
                    },
                    toBase: {
                        select: { id: true, name: true },
                    },
                    items: {
                        include: {
                            asset: {
                                select: { id: true, name: true },
                            },
                        },
                    },
                },
            });
            if (!transfer) {
                return res.status(404).json({ error: 'Transfer not found' });
            }
            if (transfer.status === enums_1.TransferStatus.COMPLETED || transfer.status === enums_1.TransferStatus.CANCELLED) {
                return res.status(400).json({ error: 'Cannot modify completed or cancelled transfer' });
            }
            if (status === enums_1.TransferStatus.APPROVED || status === enums_1.TransferStatus.CANCELLED) {
                if (user.role !== enums_1.UserRole.ADMIN && user.role !== enums_1.UserRole.BASE_COMMANDER) {
                    return res.status(403).json({ error: 'Insufficient permissions to approve/cancel transfers' });
                }
                if (user.role === enums_1.UserRole.BASE_COMMANDER && user.baseId !== transfer.toBaseId) {
                    return res.status(403).json({ error: 'Can only approve transfers to your base' });
                }
            }
            const updatedTransfer = await prisma.$transaction(async (tx) => {
                let updateData = { status };
                if (status === enums_1.TransferStatus.APPROVED) {
                    updateData.approvedById = user.id;
                    updateData.approvedAt = new Date();
                    updateData.status = enums_1.TransferStatus.IN_TRANSIT;
                }
                else if (status === enums_1.TransferStatus.COMPLETED) {
                    updateData.completedAt = new Date();
                    for (const item of transfer.items) {
                        await tx.inventory.update({
                            where: {
                                baseId_assetId: {
                                    baseId: transfer.fromBaseId,
                                    assetId: item.assetId,
                                },
                            },
                            data: {
                                currentBalance: {
                                    decrement: item.quantity,
                                },
                            },
                        });
                        await tx.inventory.upsert({
                            where: {
                                baseId_assetId: {
                                    baseId: transfer.toBaseId,
                                    assetId: item.assetId,
                                },
                            },
                            update: {
                                currentBalance: {
                                    increment: item.quantity,
                                },
                                availableCount: {
                                    increment: item.quantity,
                                },
                            },
                            create: {
                                baseId: transfer.toBaseId,
                                assetId: item.assetId,
                                openingBalance: item.quantity,
                                currentBalance: item.quantity,
                                assignedCount: 0,
                                availableCount: item.quantity,
                            },
                        });
                        await tx.transaction.create({
                            data: {
                                type: enums_1.TransactionType.TRANSFER_OUT,
                                assetId: item.assetId,
                                baseId: transfer.fromBaseId,
                                quantity: -item.quantity,
                                referenceId: transfer.id,
                                description: `Transfer to ${transfer.toBase?.name}`,
                                createdById: user.id,
                                approvedById: transfer.approvedById,
                            },
                        });
                        await tx.transaction.create({
                            data: {
                                type: enums_1.TransactionType.TRANSFER_IN,
                                assetId: item.assetId,
                                baseId: transfer.toBaseId,
                                quantity: item.quantity,
                                referenceId: transfer.id,
                                description: `Transfer from ${transfer.fromBase?.name}`,
                                createdById: user.id,
                                approvedById: transfer.approvedById,
                            },
                        });
                    }
                }
                else if (status === enums_1.TransferStatus.CANCELLED) {
                    for (const item of transfer.items) {
                        await tx.inventory.update({
                            where: {
                                baseId_assetId: {
                                    baseId: transfer.fromBaseId,
                                    assetId: item.assetId,
                                },
                            },
                            data: {
                                availableCount: {
                                    increment: item.quantity,
                                },
                            },
                        });
                    }
                }
                return await tx.transfer.update({
                    where: { id },
                    data: updateData,
                    include: {
                        fromBase: {
                            select: { id: true, name: true, code: true },
                        },
                        toBase: {
                            select: { id: true, name: true, code: true },
                        },
                        items: {
                            include: {
                                asset: {
                                    select: { id: true, name: true, code: true, category: true },
                                },
                            },
                        },
                    },
                });
            });
            res.json(updatedTransfer);
        }
        catch (error) {
            console.error('Error updating transfer status:', error);
            res.status(500).json({ error: 'Failed to update transfer status' });
        }
    },
    async delete(req, res) {
        try {
            const { id } = req.params;
            const user = req.user;
            const transfer = await prisma.transfer.findUnique({
                where: { id },
                include: {
                    items: true,
                },
            });
            if (!transfer) {
                return res.status(404).json({ error: 'Transfer not found' });
            }
            if (transfer.status !== enums_1.TransferStatus.PENDING) {
                return res.status(400).json({ error: 'Can only cancel pending transfers' });
            }
            if (user.role !== enums_1.UserRole.ADMIN && user.baseId !== transfer.fromBaseId) {
                return res.status(403).json({ error: 'Can only cancel transfers from your base' });
            }
            await prisma.$transaction(async (tx) => {
                for (const item of transfer.items) {
                    await tx.inventory.update({
                        where: {
                            baseId_assetId: {
                                baseId: transfer.fromBaseId,
                                assetId: item.assetId,
                            },
                        },
                        data: {
                            availableCount: {
                                increment: item.quantity,
                            },
                        },
                    });
                }
                await tx.transferItem.deleteMany({
                    where: { transferId: id },
                });
                await tx.transfer.delete({
                    where: { id },
                });
            });
            res.json({ message: 'Transfer cancelled successfully' });
        }
        catch (error) {
            console.error('Error cancelling transfer:', error);
            res.status(500).json({ error: 'Failed to cancel transfer' });
        }
    },
};
//# sourceMappingURL=transferController.js.map