import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from './auth';
export declare const auditLogger: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const createAuditLog: (userId: string, action: string, resource: string, resourceId?: string, oldValues?: any, newValues?: any, ipAddress?: string, userAgent?: string) => Promise<void>;
//# sourceMappingURL=auditLogger.d.ts.map