"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authController = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
const generateToken = (userId) => {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        throw new Error('JWT_SECRET not configured');
    }
    return jsonwebtoken_1.default.sign({ userId }, jwtSecret, { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
};
const generateRefreshToken = (userId) => {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        throw new Error('JWT_SECRET not configured');
    }
    return jsonwebtoken_1.default.sign({ userId, type: 'refresh' }, jwtSecret, { expiresIn: '7d' });
};
exports.authController = {
    login: async (req, res) => {
        const { email, password } = req.body;
        const user = await prisma.user.findUnique({
            where: { email },
            include: {
                base: {
                    select: {
                        id: true,
                        name: true,
                        code: true,
                    },
                },
            },
        });
        if (!user || !user.isActive) {
            throw new errorHandler_1.UnauthorizedError('Invalid credentials');
        }
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            throw new errorHandler_1.UnauthorizedError('Invalid credentials');
        }
        await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() },
        });
        const token = generateToken(user.id);
        const refreshToken = generateRefreshToken(user.id);
        logger_1.logger.info(`User logged in: ${user.email}`);
        res.json({
            message: 'Login successful',
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                base: user.base,
                lastLogin: user.lastLogin,
            },
            token,
            refreshToken,
        });
    },
    register: async (req, res) => {
        const { email, username, firstName, lastName, password, role, baseId } = req.body;
        const existingUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { email },
                    { username },
                ],
            },
        });
        if (existingUser) {
            throw new errorHandler_1.ConflictError('User with this email or username already exists');
        }
        if (role !== enums_1.UserRole.ADMIN && !baseId) {
            throw new errorHandler_1.ValidationError('Base assignment is required for this role');
        }
        if (baseId) {
            const base = await prisma.base.findUnique({
                where: { id: baseId },
            });
            if (!base || !base.isActive) {
                throw new errorHandler_1.ValidationError('Invalid base assignment');
            }
        }
        const hashedPassword = await bcryptjs_1.default.hash(password, 12);
        const user = await prisma.user.create({
            data: {
                email,
                username,
                firstName,
                lastName,
                password: hashedPassword,
                role,
                baseId: baseId || null,
            },
            include: {
                base: {
                    select: {
                        id: true,
                        name: true,
                        code: true,
                    },
                },
            },
        });
        logger_1.logger.info(`New user registered: ${user.email} (${user.role})`);
        res.status(201).json({
            message: 'User registered successfully',
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                base: user.base,
            },
        });
    },
    refreshToken: async (req, res) => {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            throw new errorHandler_1.ValidationError('Refresh token is required');
        }
        try {
            const jwtSecret = process.env.JWT_SECRET;
            if (!jwtSecret) {
                throw new Error('JWT_SECRET not configured');
            }
            const decoded = jsonwebtoken_1.default.verify(refreshToken, jwtSecret);
            if (decoded.type !== 'refresh') {
                throw new errorHandler_1.UnauthorizedError('Invalid refresh token');
            }
            const user = await prisma.user.findUnique({
                where: { id: decoded.userId },
            });
            if (!user || !user.isActive) {
                throw new errorHandler_1.UnauthorizedError('Invalid user');
            }
            const newToken = generateToken(user.id);
            const newRefreshToken = generateRefreshToken(user.id);
            res.json({
                message: 'Token refreshed successfully',
                token: newToken,
                refreshToken: newRefreshToken,
            });
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new errorHandler_1.UnauthorizedError('Invalid refresh token');
            }
            throw error;
        }
    },
    logout: async (req, res) => {
        logger_1.logger.info(`User logged out: ${req.user?.email}`);
        res.json({
            message: 'Logout successful',
        });
    },
    getCurrentUser: async (req, res) => {
        if (!req.user) {
            throw new errorHandler_1.UnauthorizedError('User not authenticated');
        }
        const user = await prisma.user.findUnique({
            where: { id: req.user.id },
            select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true,
                role: true,
                baseId: true,
                isActive: true,
                lastLogin: true,
                createdAt: true,
                base: {
                    select: {
                        id: true,
                        name: true,
                        code: true,
                        location: true,
                    },
                },
            },
        });
        if (!user) {
            throw new errorHandler_1.UnauthorizedError('User not found');
        }
        res.json({
            user,
        });
    },
    updateProfile: async (req, res) => {
        if (!req.user) {
            throw new errorHandler_1.UnauthorizedError('User not authenticated');
        }
        const { firstName, lastName } = req.body;
        const updatedUser = await prisma.user.update({
            where: { id: req.user.id },
            data: {
                firstName,
                lastName,
            },
            select: {
                id: true,
                email: true,
                username: true,
                firstName: true,
                lastName: true,
                role: true,
                base: {
                    select: {
                        id: true,
                        name: true,
                        code: true,
                    },
                },
            },
        });
        logger_1.logger.info(`User profile updated: ${req.user.email}`);
        res.json({
            message: 'Profile updated successfully',
            user: updatedUser,
        });
    },
    changePassword: async (req, res) => {
        if (!req.user) {
            throw new errorHandler_1.UnauthorizedError('User not authenticated');
        }
        const { currentPassword, newPassword } = req.body;
        if (!currentPassword || !newPassword) {
            throw new errorHandler_1.ValidationError('Current password and new password are required');
        }
        const user = await prisma.user.findUnique({
            where: { id: req.user.id },
        });
        if (!user) {
            throw new errorHandler_1.UnauthorizedError('User not found');
        }
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new errorHandler_1.UnauthorizedError('Current password is incorrect');
        }
        const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
        await prisma.user.update({
            where: { id: user.id },
            data: { password: hashedNewPassword },
        });
        logger_1.logger.info(`Password changed for user: ${user.email}`);
        res.json({
            message: 'Password changed successfully',
        });
    },
};
//# sourceMappingURL=authController.js.map