import { Router } from 'express';
import { authenticate, authorize, authorizeBase, authorizeResourceOwnership } from '../middleware/auth';
import { validate, validateQuery } from '../utils/validation';
import { expenditureSchemas, querySchemas } from '../utils/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { UserRole } from '../types/enums';
import { expenditureController } from '../controllers/expenditureController';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/expenditures - List expenditures
router.get(
  '/',
  validateQuery(querySchemas.pagination.concat(querySchemas.baseFilter).concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(expenditureController.getAll)
);

// GET /api/expenditures/summary - Get expenditure summary
router.get(
  '/summary',
  validateQuery(querySchemas.baseFilter.concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(expenditureController.getSummary)
);

// GET /api/expenditures/:id - Get specific expenditure
router.get(
  '/:id',
  authorizeResourceOwnership('expenditure'),
  asyncHandler(expenditureController.getById)
);

// POST /api/expenditures - Create new expenditure
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER, UserRole.LOGISTICS_OFFICER]),
  validate(expenditureSchemas.create),
  authorizeBase,
  asyncHandler(expenditureController.create)
);

// PUT /api/expenditures/:id - Update expenditure
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  validate(expenditureSchemas.update),
  authorizeResourceOwnership('expenditure'),
  asyncHandler(expenditureController.update)
);

export default router;
