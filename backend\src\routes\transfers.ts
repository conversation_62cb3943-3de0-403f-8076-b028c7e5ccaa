import { Router } from 'express';
import { authenticate, authorize, authorizeBase, authorizeResourceOwnership } from '../middleware/auth';
import { validate, validateQuery } from '../utils/validation';
import { transferSchemas, querySchemas } from '../utils/validation';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { UserRole } from '../types/enums';
import { transferController } from '../controllers/transferController';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/transfers - List transfers
router.get(
  '/',
  validateQuery(querySchemas.pagination.concat(querySchemas.baseFilter).concat(querySchemas.dateRange)),
  asyncHandler(transferController.getAll)
);

// GET /api/transfers/:id - Get specific transfer
router.get(
  '/:id',
  asyncHandler(transferController.getById)
);

// POST /api/transfers - Create new transfer request
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER, UserRole.LOGISTICS_OFFICER]),
  validate(transferSchemas.create),
  asyncHandler(transferController.create)
);

// PUT /api/transfers/:id/status - Update transfer status (approve/reject)
router.put(
  '/:id/status',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  validate(transferSchemas.updateStatus),
  asyncHandler(transferController.updateStatus)
);

// DELETE /api/transfers/:id - Cancel transfer (before approval)
router.delete(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  asyncHandler(transferController.delete)
);

export default router;
