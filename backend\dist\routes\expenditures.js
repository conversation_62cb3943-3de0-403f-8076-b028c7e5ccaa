"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const enums_1 = require("../types/enums");
const expenditureController_1 = require("../controllers/expenditureController");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.baseFilter).concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(expenditureController_1.expenditureController.getAll));
router.get('/summary', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter.concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(expenditureController_1.expenditureController.getSummary));
router.get('/:id', (0, auth_1.authorizeResourceOwnership)('expenditure'), (0, errorHandler_1.asyncHandler)(expenditureController_1.expenditureController.getById));
router.post('/', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER, enums_1.UserRole.LOGISTICS_OFFICER]), (0, validation_1.validate)(validation_2.expenditureSchemas.create), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(expenditureController_1.expenditureController.create));
router.put('/:id', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validate)(validation_2.expenditureSchemas.update), (0, auth_1.authorizeResourceOwnership)('expenditure'), (0, errorHandler_1.asyncHandler)(expenditureController_1.expenditureController.update));
exports.default = router;
//# sourceMappingURL=expenditures.js.map