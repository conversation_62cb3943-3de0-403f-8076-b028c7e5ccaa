"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const enums_1 = require("../types/enums");
const transferController_1 = require("../controllers/transferController");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.baseFilter).concat(validation_2.querySchemas.dateRange)), (0, errorHandler_1.asyncHandler)(transferController_1.transferController.getAll));
router.get('/:id', (0, errorHandler_1.asyncHandler)(transferController_1.transferController.getById));
router.post('/', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER, enums_1.UserRole.LOGISTICS_OFFICER]), (0, validation_1.validate)(validation_2.transferSchemas.create), (0, errorHandler_1.asyncHandler)(transferController_1.transferController.create));
router.put('/:id/status', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validate)(validation_2.transferSchemas.updateStatus), (0, errorHandler_1.asyncHandler)(transferController_1.transferController.updateStatus));
router.delete('/:id', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, errorHandler_1.asyncHandler)(transferController_1.transferController.delete));
exports.default = router;
//# sourceMappingURL=transfers.js.map