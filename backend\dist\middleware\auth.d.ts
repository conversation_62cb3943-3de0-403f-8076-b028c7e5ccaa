import { Request, Response, NextFunction } from 'express';
import { UserRoleType } from '../types/enums';
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email: string;
        username: string;
        role: UserRoleType;
        baseId?: string;
    };
}
export declare const authenticate: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const authorize: (allowedRoles: UserRoleType[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const authorizeBase: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const authorizeResourceOwnership: (resourceType: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=auth.d.ts.map