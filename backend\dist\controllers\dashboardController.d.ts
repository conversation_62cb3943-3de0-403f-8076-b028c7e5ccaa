import { Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const dashboardController: {
    getOverview: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getMetrics: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getNetMovementDetails: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getRecentActivities: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getInventoryAlerts: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getChartsData: (req: AuthenticatedRequest, res: Response) => Promise<void>;
};
//# sourceMappingURL=dashboardController.d.ts.map