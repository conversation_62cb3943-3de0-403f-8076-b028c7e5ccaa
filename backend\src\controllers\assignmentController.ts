import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { UserRole } from '../types/enums';

const prisma = new PrismaClient();

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    baseId: string;
  };
}

export const assignmentController = {
  // Get all assignments with filtering and pagination
  async getAll(req: AuthenticatedRequest, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        baseId,
        assetId,
        status,
        assignedTo,
        startDate,
        endDate,
      } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      // Build where clause based on user role and filters
      let whereClause: any = {};

      // Role-based filtering
      if (req.user?.role === UserRole.BASE_COMMANDER || req.user?.role === UserRole.LOGISTICS_OFFICER) {
        whereClause.baseId = req.user.baseId;
      }

      // Apply additional filters
      if (baseId) whereClause.baseId = baseId as string;
      if (assetId) whereClause.assetId = assetId as string;
      if (status) whereClause.status = status as string;
      if (assignedTo) whereClause.assignedTo = assignedTo as string;

      // Date range filtering
      if (startDate || endDate) {
        whereClause.assignedAt = {};
        if (startDate) whereClause.assignedAt.gte = new Date(startDate as string);
        if (endDate) whereClause.assignedAt.lte = new Date(endDate as string);
      }

      const [assignments, total] = await Promise.all([
        prisma.assetAssignment.findMany({
          where: whereClause,
          include: {
            base: {
              select: { id: true, name: true, code: true }
            },
            asset: {
              select: { id: true, name: true, code: true, category: true }
            },
            user: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { assignedAt: 'desc' },
          skip,
          take,
        }),
        prisma.assetAssignment.count({ where: whereClause }),
      ]);

      const totalPages = Math.ceil(total / take);

      res.json({
        assignments,
        pagination: {
          page: Number(page),
          limit: take,
          total,
          pages: totalPages,
        },
      });
    } catch (error) {
      console.error('Error fetching assignments:', error);
      res.status(500).json({ error: 'Failed to fetch assignments' });
    }
  },

  // Get assignment by ID
  async getById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;

      const assignment = await prisma.assetAssignment.findUnique({
        where: { id },
        include: {
          base: {
            select: { id: true, name: true, code: true }
          },
          asset: {
            select: { id: true, name: true, code: true, category: true, description: true }
          },
          user: {
            select: { id: true, name: true, email: true }
          }
        },
      });

      if (!assignment) {
        return res.status(404).json({ error: 'Assignment not found' });
      }

      // Check authorization
      if (req.user?.role !== UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json({ assignment });
    } catch (error) {
      console.error('Error fetching assignment:', error);
      res.status(500).json({ error: 'Failed to fetch assignment' });
    }
  },

  // Create new assignment
  async create(req: AuthenticatedRequest, res: Response) {
    try {
      const { baseId, assetId, assignedTo, quantity, description } = req.body;

      // Validate base access
      if (req.user?.role !== UserRole.ADMIN && baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Cannot create assignments for other bases' });
      }

      // Check if asset exists and has sufficient inventory
      const inventory = await prisma.inventory.findUnique({
        where: {
          baseId_assetId: {
            baseId,
            assetId,
          }
        },
        include: {
          asset: true
        }
      });

      if (!inventory) {
        return res.status(400).json({ error: 'Asset not available at this base' });
      }

      if (inventory.availableCount < quantity) {
        return res.status(400).json({ 
          error: `Insufficient inventory. Available: ${inventory.availableCount}, Requested: ${quantity}` 
        });
      }

      // Check if user exists
      const assignedUser = await prisma.user.findUnique({
        where: { id: assignedTo }
      });

      if (!assignedUser) {
        return res.status(400).json({ error: 'Assigned user not found' });
      }

      // Create assignment and update inventory in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the assignment
        const assignment = await tx.assetAssignment.create({
          data: {
            baseId,
            assetId,
            assignedTo,
            quantity,
            description,
            status: 'ASSIGNED',
          },
          include: {
            base: {
              select: { id: true, name: true, code: true }
            },
            asset: {
              select: { id: true, name: true, code: true, category: true }
            },
            user: {
              select: { id: true, name: true, email: true }
            }
          },
        });

        // Update inventory - reduce available count
        await tx.inventory.update({
          where: {
            baseId_assetId: {
              baseId,
              assetId,
            }
          },
          data: {
            availableCount: {
              decrement: quantity
            }
          }
        });

        // Create audit log
        await tx.auditLog.create({
          data: {
            userId: req.user!.id,
            action: 'CREATE',
            resource: 'ASSIGNMENT',
            resourceId: assignment.id,
            newValues: JSON.stringify({
              baseId,
              assetId,
              assignedTo,
              quantity,
              description,
            }),
          }
        });

        return assignment;
      });

      res.status(201).json({ assignment: result });
    } catch (error) {
      console.error('Error creating assignment:', error);
      res.status(500).json({ error: 'Failed to create assignment' });
    }
  },

  // Return assigned asset
  async returnAsset(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { returnCondition, notes } = req.body;

      const assignment = await prisma.assetAssignment.findUnique({
        where: { id },
        include: {
          base: true,
          asset: true,
        }
      });

      if (!assignment) {
        return res.status(404).json({ error: 'Assignment not found' });
      }

      if (assignment.status !== 'ASSIGNED') {
        return res.status(400).json({ error: 'Asset is not currently assigned' });
      }

      // Check authorization
      if (req.user?.role !== UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      // Return asset and update inventory in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Update assignment status
        const updatedAssignment = await tx.assetAssignment.update({
          where: { id },
          data: {
            status: 'RETURNED',
            returnedAt: new Date(),
            description: assignment.description ? 
              `${assignment.description}\n\nReturned: ${notes || 'No notes'}` : 
              `Returned: ${notes || 'No notes'}`,
          },
          include: {
            base: {
              select: { id: true, name: true, code: true }
            },
            asset: {
              select: { id: true, name: true, code: true, category: true }
            },
            user: {
              select: { id: true, name: true, email: true }
            }
          },
        });

        // Update inventory - increase available count
        await tx.inventory.update({
          where: {
            baseId_assetId: {
              baseId: assignment.baseId,
              assetId: assignment.assetId,
            }
          },
          data: {
            availableCount: {
              increment: assignment.quantity
            }
          }
        });

        // Create audit log
        await tx.auditLog.create({
          data: {
            userId: req.user!.id,
            action: 'UPDATE',
            resource: 'ASSIGNMENT',
            resourceId: assignment.id,
            oldValues: JSON.stringify({ status: 'ASSIGNED' }),
            newValues: JSON.stringify({ 
              status: 'RETURNED', 
              returnCondition, 
              notes 
            }),
          }
        });

        return updatedAssignment;
      });

      res.json({ assignment: result });
    } catch (error) {
      console.error('Error returning asset:', error);
      res.status(500).json({ error: 'Failed to return asset' });
    }
  },

  // Update assignment
  async update(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { status, description } = req.body;

      const assignment = await prisma.assetAssignment.findUnique({
        where: { id }
      });

      if (!assignment) {
        return res.status(404).json({ error: 'Assignment not found' });
      }

      // Check authorization
      if (req.user?.role !== UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const updatedAssignment = await prisma.assetAssignment.update({
        where: { id },
        data: {
          ...(status && { status }),
          ...(description && { description }),
        },
        include: {
          base: {
            select: { id: true, name: true, code: true }
          },
          asset: {
            select: { id: true, name: true, code: true, category: true }
          },
          user: {
            select: { id: true, name: true, email: true }
          }
        },
      });

      // Create audit log
      await prisma.auditLog.create({
        data: {
          userId: req.user!.id,
          action: 'UPDATE',
          resource: 'ASSIGNMENT',
          resourceId: id,
          oldValues: JSON.stringify({
            status: assignment.status,
            description: assignment.description,
          }),
          newValues: JSON.stringify({ status, description }),
        }
      });

      res.json({ assignment: updatedAssignment });
    } catch (error) {
      console.error('Error updating assignment:', error);
      res.status(500).json({ error: 'Failed to update assignment' });
    }
  },
};
