import { Response } from 'express';
import { PrismaClient, TransactionType, UserRole } from '@prisma/client';
import { AuthenticatedRequest } from '../middleware/auth';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export const dashboardController = {
  // Get dashboard overview
  getOverview: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId, startDate, endDate } = req.query;
    const user = req.user!;

    // Determine which bases to query
    let baseFilter: any = {};
    if (user.role !== UserRole.ADMIN) {
      baseFilter = { id: user.baseId };
    } else if (baseId) {
      baseFilter = { id: baseId as string };
    }

    // Date filter for transactions
    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.lte = new Date(endDate as string);
    }

    // Get inventory summary
    const inventorySummary = await prisma.inventory.aggregate({
      where: { base: baseFilter },
      _sum: {
        openingBalance: true,
        currentBalance: true,
        assignedCount: true,
        availableCount: true,
      },
    });

    // Get transaction summary
    const transactionSummary = await prisma.transaction.groupBy({
      by: ['type'],
      where: {
        ...dateFilter,
        ...(Object.keys(baseFilter).length > 0 && {
          asset: {
            inventory: {
              some: { base: baseFilter }
            }
          }
        })
      },
      _sum: {
        quantity: true,
        totalValue: true,
      },
    });

    // Calculate net movement
    const purchases = transactionSummary.find(t => t.type === TransactionType.PURCHASE)?._sum.quantity || 0;
    const transfersIn = transactionSummary.find(t => t.type === TransactionType.TRANSFER_IN)?._sum.quantity || 0;
    const transfersOut = transactionSummary.find(t => t.type === TransactionType.TRANSFER_OUT)?._sum.quantity || 0;
    const assignments = transactionSummary.find(t => t.type === TransactionType.ASSIGNMENT)?._sum.quantity || 0;
    const expenditures = transactionSummary.find(t => t.type === TransactionType.EXPENDITURE)?._sum.quantity || 0;

    const netMovement = purchases + transfersIn - transfersOut - assignments - expenditures;

    // Get base information
    const bases = await prisma.base.findMany({
      where: baseFilter,
      select: {
        id: true,
        name: true,
        code: true,
        location: true,
      },
    });

    res.json({
      overview: {
        openingBalance: inventorySummary._sum.openingBalance || 0,
        currentBalance: inventorySummary._sum.currentBalance || 0,
        assignedCount: inventorySummary._sum.assignedCount || 0,
        availableCount: inventorySummary._sum.availableCount || 0,
        netMovement,
        purchases,
        transfersIn,
        transfersOut,
        assignments,
        expenditures,
      },
      bases,
      dateRange: {
        startDate: startDate || null,
        endDate: endDate || null,
      },
    });
  },

  // Get key metrics
  getMetrics: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId, startDate, endDate } = req.query;
    const user = req.user!;

    let baseFilter: any = {};
    if (user.role !== UserRole.ADMIN) {
      baseFilter = { id: user.baseId };
    } else if (baseId) {
      baseFilter = { id: baseId as string };
    }

    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.lte = new Date(endDate as string);
    }

    // Get metrics by category
    const categoryMetrics = await prisma.inventory.groupBy({
      by: ['asset'],
      where: { base: baseFilter },
      _sum: {
        currentBalance: true,
        assignedCount: true,
        availableCount: true,
      },
      include: {
        asset: {
          select: {
            category: true,
            name: true,
          },
        },
      },
    });

    // Get recent transaction trends
    const recentTransactions = await prisma.transaction.findMany({
      where: {
        ...dateFilter,
        ...(Object.keys(baseFilter).length > 0 && {
          asset: {
            inventory: {
              some: { base: baseFilter }
            }
          }
        })
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        asset: {
          select: {
            name: true,
            category: true,
          },
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    res.json({
      categoryMetrics,
      recentTransactions,
    });
  },

  // Get net movement details for popup
  getNetMovementDetails: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId } = req.params;
    const { startDate, endDate } = req.query;
    const user = req.user!;

    // Authorization check
    if (user.role !== UserRole.ADMIN && user.baseId !== baseId) {
      res.status(403).json({ error: 'Access denied to this base' });
      return;
    }

    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.lte = new Date(endDate as string);
    }

    // Get detailed breakdown
    const purchases = await prisma.transaction.findMany({
      where: {
        type: TransactionType.PURCHASE,
        ...dateFilter,
        asset: {
          inventory: {
            some: { baseId }
          }
        }
      },
      include: {
        asset: { select: { name: true, category: true } },
        createdBy: { select: { firstName: true, lastName: true } },
      },
      orderBy: { createdAt: 'desc' },
    });

    const transfersIn = await prisma.transaction.findMany({
      where: {
        type: TransactionType.TRANSFER_IN,
        ...dateFilter,
        asset: {
          inventory: {
            some: { baseId }
          }
        }
      },
      include: {
        asset: { select: { name: true, category: true } },
        createdBy: { select: { firstName: true, lastName: true } },
      },
      orderBy: { createdAt: 'desc' },
    });

    const transfersOut = await prisma.transaction.findMany({
      where: {
        type: TransactionType.TRANSFER_OUT,
        ...dateFilter,
        asset: {
          inventory: {
            some: { baseId }
          }
        }
      },
      include: {
        asset: { select: { name: true, category: true } },
        createdBy: { select: { firstName: true, lastName: true } },
      },
      orderBy: { createdAt: 'desc' },
    });

    res.json({
      purchases: {
        count: purchases.length,
        totalQuantity: purchases.reduce((sum, t) => sum + t.quantity, 0),
        totalValue: purchases.reduce((sum, t) => sum + (t.totalValue?.toNumber() || 0), 0),
        transactions: purchases,
      },
      transfersIn: {
        count: transfersIn.length,
        totalQuantity: transfersIn.reduce((sum, t) => sum + t.quantity, 0),
        transactions: transfersIn,
      },
      transfersOut: {
        count: transfersOut.length,
        totalQuantity: transfersOut.reduce((sum, t) => sum + t.quantity, 0),
        transactions: transfersOut,
      },
    });
  },

  // Get recent activities
  getRecentActivities: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId, page = 1, limit = 10 } = req.query;
    const user = req.user!;

    let baseFilter: any = {};
    if (user.role !== UserRole.ADMIN) {
      baseFilter = { baseId: user.baseId };
    } else if (baseId) {
      baseFilter = { baseId: baseId as string };
    }

    const skip = (Number(page) - 1) * Number(limit);

    const activities = await prisma.transaction.findMany({
      where: baseFilter,
      orderBy: { createdAt: 'desc' },
      skip,
      take: Number(limit),
      include: {
        asset: {
          select: {
            name: true,
            category: true,
          },
        },
        createdBy: {
          select: {
            firstName: true,
            lastName: true,
            username: true,
          },
        },
      },
    });

    const totalCount = await prisma.transaction.count({
      where: baseFilter,
    });

    res.json({
      activities,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / Number(limit)),
      },
    });
  },

  // Get inventory alerts
  getInventoryAlerts: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId } = req.query;
    const user = req.user!;

    let baseFilter: any = {};
    if (user.role !== UserRole.ADMIN) {
      baseFilter = { id: user.baseId };
    } else if (baseId) {
      baseFilter = { id: baseId as string };
    }

    // Low stock alerts (less than 10% of opening balance or less than 5 units)
    const lowStockItems = await prisma.inventory.findMany({
      where: {
        base: baseFilter,
        OR: [
          {
            availableCount: {
              lt: prisma.inventory.fields.openingBalance.divide(10),
            },
          },
          {
            availableCount: {
              lt: 5,
            },
          },
        ],
      },
      include: {
        asset: {
          select: {
            name: true,
            category: true,
          },
        },
        base: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    // Overdue assignments (assigned for more than 30 days)
    const overdueAssignments = await prisma.assetAssignment.findMany({
      where: {
        base: baseFilter,
        assignedAt: {
          lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        },
        returnedAt: null,
      },
      include: {
        asset: {
          select: {
            name: true,
            category: true,
          },
        },
        base: {
          select: {
            name: true,
            code: true,
          },
        },
      },
    });

    res.json({
      alerts: {
        lowStock: lowStockItems,
        overdueAssignments,
      },
      summary: {
        lowStockCount: lowStockItems.length,
        overdueAssignmentsCount: overdueAssignments.length,
      },
    });
  },

  // Get charts data
  getChartsData: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    const { baseId, startDate, endDate } = req.query;
    const user = req.user!;

    let baseFilter: any = {};
    if (user.role !== UserRole.ADMIN) {
      baseFilter = { baseId: user.baseId };
    } else if (baseId) {
      baseFilter = { baseId: baseId as string };
    }

    const dateFilter: any = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) dateFilter.createdAt.gte = new Date(startDate as string);
      if (endDate) dateFilter.createdAt.lte = new Date(endDate as string);
    }

    // Transaction trends over time
    const transactionTrends = await prisma.transaction.groupBy({
      by: ['type', 'createdAt'],
      where: { ...baseFilter, ...dateFilter },
      _sum: {
        quantity: true,
        totalValue: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Asset distribution by category
    const assetDistribution = await prisma.inventory.groupBy({
      by: ['assetId'],
      where: { base: { id: baseId as string || user.baseId } },
      _sum: {
        currentBalance: true,
      },
      include: {
        asset: {
          select: {
            category: true,
          },
        },
      },
    });

    res.json({
      transactionTrends,
      assetDistribution,
    });
  },
};
