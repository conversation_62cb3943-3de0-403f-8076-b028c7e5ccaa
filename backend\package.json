{"name": "military-backend", "version": "1.0.0", "description": "Backend API for Military Asset Management System", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "tsx src/scripts/seed.ts", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@prisma/client": "^5.7.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.523.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "postcss": "^8.5.6", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "tailwindcss": "^4.1.10", "winston": "^3.17.0", "yup": "^1.6.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["military", "asset-management", "api", "express", "postgresql", "prisma"], "author": "Military Asset Management Team", "license": "MIT"}