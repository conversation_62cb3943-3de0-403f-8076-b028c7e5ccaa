import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { validateQuery } from '../utils/validation';
import { querySchemas } from '../utils/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { UserRole } from '../types/enums';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/bases - List bases
router.get(
  '/',
  validateQuery(querySchemas.pagination),
  asyncHandler(async (_req, res) => {
    // TODO: Implement base listing (filtered by user role)
    res.json({ message: 'Base listing endpoint - TODO' });
  })
);

// GET /api/bases/:id - Get specific base
router.get(
  '/:id',
  asyncHandler(async (req, res) => {
    // TODO: Implement get base by ID (with authorization check)
    res.json({ message: 'Get base by ID endpoint - TODO' });
  })
);

// POST /api/bases - Create new base (Admin only)
router.post(
  '/',
  authorize([UserRole.ADMIN]),
  asyncHandler(async (req, res) => {
    // TODO: Implement base creation
    res.json({ message: 'Create base endpoint - TODO' });
  })
);

// PUT /api/bases/:id - Update base (Admin only)
router.put(
  '/:id',
  authorize([UserRole.ADMIN]),
  asyncHandler(async (req, res) => {
    // TODO: Implement base update
    res.json({ message: 'Update base endpoint - TODO' });
  })
);

export default router;
