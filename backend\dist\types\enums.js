"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EquipmentCategory = exports.AssetStatus = exports.TransferStatus = exports.TransactionType = exports.UserRole = void 0;
exports.UserRole = {
    ADMIN: 'ADMIN',
    BASE_COMMANDER: 'BASE_COMMANDER',
    LOGISTICS_OFFICER: 'LOGISTICS_OFFICER',
};
exports.TransactionType = {
    PURCHASE: 'PURCHASE',
    TRANSFER_IN: 'TRANSFER_IN',
    TRANSFER_OUT: 'TRANSFER_OUT',
    ASSIGNMENT: 'ASSIGNMENT',
    EXPENDITURE: 'EXPENDITURE',
    RETURN: 'RETURN',
    MAINTENANCE: 'MAINTENANCE',
};
exports.TransferStatus = {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    IN_TRANSIT: 'IN_TRANSIT',
    COMPLETED: 'COMPLETED',
    CANCELLED: 'CANCELLED',
};
exports.AssetStatus = {
    AVAILABLE: 'AVAILABLE',
    ASSIGNED: 'ASSIGNED',
    IN_MAINTENANCE: 'IN_MAINTENANCE',
    DECOMMISSIONED: 'DECOMMISSIONED',
    LOST: 'LOST',
};
exports.EquipmentCategory = {
    WEAPONS: 'WEAPONS',
    VEHICLES: 'VEHICLES',
    AMMUNITION: 'AMMUNITION',
    COMMUNICATION: 'COMMUNICATION',
    MEDICAL: 'MEDICAL',
    SUPPLIES: 'SUPPLIES',
    ELECTRONICS: 'ELECTRONICS',
    PROTECTIVE_GEAR: 'PROTECTIVE_GEAR',
};
//# sourceMappingURL=enums.js.map