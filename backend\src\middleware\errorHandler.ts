import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class ValidationError extends Error {
  statusCode = 400;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  isOperational = true;

  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error {
  statusCode = 401;
  isOperational = true;

  constructor(message: string = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error {
  statusCode = 403;
  isOperational = true;

  constructor(message: string = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends Error {
  statusCode = 409;
  isOperational = true;

  constructor(message: string) {
    super(message);
    this.name = 'ConflictError';
  }
}

export class InternalServerError extends Error {
  statusCode = 500;
  isOperational = true;

  constructor(message: string = 'Internal server error') {
    super(message);
    this.name = 'InternalServerError';
  }
}

// Async handler wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};



// Handle Prisma errors
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): AppError => {
  let message = 'Database error';
  let statusCode = 500;

  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const target = error.meta?.target as string[];
      message = `Duplicate value for ${target?.join(', ') || 'field'}`;
      statusCode = 409;
      break;
    case 'P2014':
      // Invalid ID
      message = 'Invalid ID provided';
      statusCode = 400;
      break;
    case 'P2003':
      // Foreign key constraint violation
      message = 'Referenced record does not exist';
      statusCode = 400;
      break;
    case 'P2025':
      // Record not found
      message = 'Record not found';
      statusCode = 404;
      break;
    case 'P2016':
      // Query interpretation error
      message = 'Invalid query parameters';
      statusCode = 400;
      break;
    default:
      message = 'Database operation failed';
      statusCode = 500;
  }

  const appError = new Error(message) as AppError;
  appError.statusCode = statusCode;
  appError.isOperational = true;
  return appError;
};

// Main error handler middleware
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let err = error as AppError;

  // Handle Prisma errors
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    err = handlePrismaError(error);
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    err = new ValidationError('Invalid data provided');
  } else if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    err = new Error('Database connection error') as AppError;
    err.statusCode = 500;
    err.isOperational = true;
  }

  // Set default error properties
  err.statusCode = err.statusCode || 500;
  err.isOperational = err.isOperational || false;

  // Log error
  if (err.statusCode >= 500) {
    logger.error('Server Error:', {
      message: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id,
    });
  } else {
    logger.warn('Client Error:', {
      message: err.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userId: (req as any).user?.id,
    });
  }

  // Send error response
  const response: any = {
    error: err.message,
    status: err.statusCode,
    timestamp: new Date().toISOString(),
  };

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack;
    response.details = err;
  }

  // Include request ID if available
  if (req.headers['x-request-id']) {
    response.requestId = req.headers['x-request-id'];
  }

  res.status(err.statusCode).json(response);
};


