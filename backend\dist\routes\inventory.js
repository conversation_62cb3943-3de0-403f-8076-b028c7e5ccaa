"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.baseFilter).concat(validation_2.querySchemas.assetFilter)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Inventory listing endpoint - TODO' });
}));
router.get('/:baseId/:assetId', auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Get inventory item endpoint - TODO' });
}));
router.put('/:baseId/:assetId', auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Update inventory endpoint - TODO' });
}));
exports.default = router;
//# sourceMappingURL=inventory.js.map