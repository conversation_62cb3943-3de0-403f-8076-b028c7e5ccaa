"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenditureSchemas = exports.validateQuery = exports.validate = exports.querySchemas = exports.assignmentSchemas = exports.transferSchemas = exports.purchaseSchemas = exports.assetSchemas = exports.authSchemas = exports.commonSchemas = void 0;
const joi_1 = __importDefault(require("joi"));
const enums_1 = require("../types/enums");
exports.commonSchemas = {
    id: joi_1.default.string().required().messages({
        'string.empty': 'ID is required',
        'any.required': 'ID is required',
    }),
    email: joi_1.default.string().email().required().messages({
        'string.email': 'Please provide a valid email address',
        'string.empty': 'Email is required',
        'any.required': 'Email is required',
    }),
    password: joi_1.default.string().min(8).required().messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.empty': 'Password is required',
        'any.required': 'Password is required',
    }),
    positiveInteger: joi_1.default.number().integer().min(1).required().messages({
        'number.base': 'Must be a number',
        'number.integer': 'Must be an integer',
        'number.min': 'Must be greater than 0',
        'any.required': 'This field is required',
    }),
    nonNegativeInteger: joi_1.default.number().integer().min(0).required().messages({
        'number.base': 'Must be a number',
        'number.integer': 'Must be an integer',
        'number.min': 'Must be 0 or greater',
        'any.required': 'This field is required',
    }),
    price: joi_1.default.number().precision(2).min(0).messages({
        'number.base': 'Must be a number',
        'number.precision': 'Maximum 2 decimal places allowed',
        'number.min': 'Must be 0 or greater',
    }),
    date: joi_1.default.date().iso().messages({
        'date.base': 'Must be a valid date',
        'date.format': 'Date must be in ISO format',
    }),
};
exports.authSchemas = {
    login: joi_1.default.object({
        email: exports.commonSchemas.email,
        password: joi_1.default.string().required().messages({
            'string.empty': 'Password is required',
            'any.required': 'Password is required',
        }),
    }),
    register: joi_1.default.object({
        email: exports.commonSchemas.email,
        username: joi_1.default.string().alphanum().min(3).max(30).required().messages({
            'string.alphanum': 'Username must contain only letters and numbers',
            'string.min': 'Username must be at least 3 characters long',
            'string.max': 'Username must not exceed 30 characters',
            'string.empty': 'Username is required',
            'any.required': 'Username is required',
        }),
        firstName: joi_1.default.string().min(1).max(50).required().messages({
            'string.min': 'First name is required',
            'string.max': 'First name must not exceed 50 characters',
            'string.empty': 'First name is required',
            'any.required': 'First name is required',
        }),
        lastName: joi_1.default.string().min(1).max(50).required().messages({
            'string.min': 'Last name is required',
            'string.max': 'Last name must not exceed 50 characters',
            'string.empty': 'Last name is required',
            'any.required': 'Last name is required',
        }),
        password: exports.commonSchemas.password,
        role: joi_1.default.string().valid(...Object.values(enums_1.UserRole)).required().messages({
            'any.only': 'Invalid role specified',
            'any.required': 'Role is required',
        }),
        baseId: joi_1.default.string().when('role', {
            is: joi_1.default.string().valid(enums_1.UserRole.BASE_COMMANDER, enums_1.UserRole.LOGISTICS_OFFICER),
            then: joi_1.default.required(),
            otherwise: joi_1.default.optional(),
        }).messages({
            'any.required': 'Base assignment is required for this role',
        }),
    }),
};
exports.assetSchemas = {
    create: joi_1.default.object({
        name: joi_1.default.string().min(1).max(100).required().messages({
            'string.min': 'Asset name is required',
            'string.max': 'Asset name must not exceed 100 characters',
            'string.empty': 'Asset name is required',
            'any.required': 'Asset name is required',
        }),
        code: joi_1.default.string().alphanum().min(3).max(20).required().messages({
            'string.alphanum': 'Asset code must contain only letters and numbers',
            'string.min': 'Asset code must be at least 3 characters long',
            'string.max': 'Asset code must not exceed 20 characters',
            'string.empty': 'Asset code is required',
            'any.required': 'Asset code is required',
        }),
        category: joi_1.default.string().valid(...Object.values(enums_1.EquipmentCategory)).required().messages({
            'any.only': 'Invalid equipment category',
            'any.required': 'Equipment category is required',
        }),
        description: joi_1.default.string().max(500).optional().messages({
            'string.max': 'Description must not exceed 500 characters',
        }),
        unitPrice: exports.commonSchemas.price.optional(),
    }),
    update: joi_1.default.object({
        name: joi_1.default.string().min(1).max(100).optional(),
        description: joi_1.default.string().max(500).optional(),
        unitPrice: exports.commonSchemas.price.optional(),
        isActive: joi_1.default.boolean().optional(),
    }),
};
exports.purchaseSchemas = {
    create: joi_1.default.object({
        baseId: exports.commonSchemas.id,
        vendor: joi_1.default.string().min(1).max(100).required().messages({
            'string.min': 'Vendor name is required',
            'string.max': 'Vendor name must not exceed 100 characters',
            'string.empty': 'Vendor name is required',
            'any.required': 'Vendor name is required',
        }),
        description: joi_1.default.string().max(500).optional(),
        items: joi_1.default.array().items(joi_1.default.object({
            assetId: exports.commonSchemas.id,
            quantity: exports.commonSchemas.positiveInteger,
            unitPrice: exports.commonSchemas.price.required(),
        })).min(1).required().messages({
            'array.min': 'At least one item is required',
            'any.required': 'Purchase items are required',
        }),
    }),
};
exports.transferSchemas = {
    create: joi_1.default.object({
        fromBaseId: exports.commonSchemas.id,
        toBaseId: exports.commonSchemas.id.invalid(joi_1.default.ref('fromBaseId')).messages({
            'any.invalid': 'Destination base must be different from source base',
        }),
        description: joi_1.default.string().max(500).optional(),
        items: joi_1.default.array().items(joi_1.default.object({
            assetId: exports.commonSchemas.id,
            quantity: exports.commonSchemas.positiveInteger,
        })).min(1).required().messages({
            'array.min': 'At least one item is required',
            'any.required': 'Transfer items are required',
        }),
    }),
    updateStatus: joi_1.default.object({
        status: joi_1.default.string().valid(...Object.values(enums_1.TransferStatus)).required().messages({
            'any.only': 'Invalid transfer status',
            'any.required': 'Transfer status is required',
        }),
    }),
};
exports.assignmentSchemas = {
    create: joi_1.default.object({
        baseId: exports.commonSchemas.id,
        assetId: exports.commonSchemas.id,
        assignedTo: exports.commonSchemas.id,
        quantity: exports.commonSchemas.positiveInteger,
        description: joi_1.default.string().max(500).optional(),
    }),
    return: joi_1.default.object({
        quantity: exports.commonSchemas.positiveInteger.optional(),
        description: joi_1.default.string().max(500).optional(),
    }),
};
exports.querySchemas = {
    pagination: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).default(1),
        limit: joi_1.default.number().integer().min(1).max(100).default(10),
    }),
    dateRange: joi_1.default.object({
        startDate: exports.commonSchemas.date.optional(),
        endDate: exports.commonSchemas.date.optional().when('startDate', {
            is: joi_1.default.exist(),
            then: joi_1.default.date().min(joi_1.default.ref('startDate')),
        }),
    }),
    baseFilter: joi_1.default.object({
        baseId: exports.commonSchemas.id.optional(),
    }),
    assetFilter: joi_1.default.object({
        category: joi_1.default.string().valid(...Object.values(enums_1.EquipmentCategory)).optional(),
        assetId: exports.commonSchemas.id.optional(),
    }),
};
const validate = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, {
            abortEarly: false,
            stripUnknown: true,
        });
        if (error) {
            const errors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
            }));
            return res.status(400).json({
                error: 'Validation failed',
                details: errors,
            });
        }
        req.body = value;
        next();
    };
};
exports.validate = validate;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.query, {
            abortEarly: false,
            stripUnknown: true,
        });
        if (error) {
            const errors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
            }));
            return res.status(400).json({
                error: 'Query validation failed',
                details: errors,
            });
        }
        req.query = value;
        next();
    };
};
exports.validateQuery = validateQuery;
exports.expenditureSchemas = {
    create: joi_1.default.object({
        baseId: exports.commonSchemas.id,
        assetId: exports.commonSchemas.id,
        quantity: exports.commonSchemas.positiveInteger,
        type: joi_1.default.string().valid('EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL').required(),
        unitPrice: joi_1.default.number().positive().optional(),
        description: joi_1.default.string().max(1000).optional(),
        reason: joi_1.default.string().max(500).optional(),
    }),
    update: joi_1.default.object({
        description: joi_1.default.string().max(1000).optional(),
        unitPrice: joi_1.default.number().positive().optional(),
    }),
};
//# sourceMappingURL=validation.js.map