import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { AuthenticatedRequest } from './auth';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

// Actions that should be audited
const AUDITABLE_ACTIONS = [
  'POST',
  'PUT',
  'PATCH',
  'DELETE',
];

// Resources that should be audited
const AUDITABLE_RESOURCES = [
  '/api/purchases',
  '/api/transfers',
  '/api/assignments',
  '/api/assets',
  '/api/inventory',
  '/api/users',
  '/api/bases',
];

// Extract resource information from URL
const extractResourceInfo = (url: string, method: string) => {
  const segments = url.split('/').filter(Boolean);
  
  if (segments.length < 2) return null;
  
  const resource = segments[1]; // 'api'
  const resourceType = segments[2]; // 'purchases', 'transfers', etc.
  const resourceId = segments[3]; // specific ID if present
  
  return {
    resource: resourceType,
    resourceId: resourceId || null,
    action: getActionFromMethod(method, !!resourceId),
  };
};

// Map HTTP methods to audit actions
const getActionFromMethod = (method: string, hasId: boolean): string => {
  switch (method) {
    case 'POST':
      return 'CREATE';
    case 'PUT':
    case 'PATCH':
      return 'UPDATE';
    case 'DELETE':
      return 'DELETE';
    case 'GET':
      return hasId ? 'VIEW' : 'LIST';
    default:
      return method;
  }
};

// Capture request body for audit
const captureRequestData = (req: Request): any => {
  const sensitiveFields = ['password', 'token', 'secret'];
  const data = { ...req.body };
  
  // Remove sensitive fields
  sensitiveFields.forEach(field => {
    if (data[field]) {
      data[field] = '[REDACTED]';
    }
  });
  
  return Object.keys(data).length > 0 ? data : null;
};

// Audit logging middleware
export const auditLogger = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  // Skip non-auditable actions or resources
  if (!AUDITABLE_ACTIONS.includes(req.method) || 
      !AUDITABLE_RESOURCES.some(resource => req.path.startsWith(resource))) {
    next();
    return;
  }

  // Skip health checks and other system endpoints
  if (req.path.includes('/health') || req.path.includes('/docs')) {
    next();
    return;
  }

  const startTime = Date.now();
  const originalSend = res.send;
  let responseData: any = null;

  // Capture response data
  res.send = function(data: any) {
    responseData = data;
    return originalSend.call(this, data);
  };

  // Continue with request processing
  next();

  // Log audit after response is sent
  res.on('finish', async () => {
    try {
      // Only log if user is authenticated
      if (!req.user) return;

      const resourceInfo = extractResourceInfo(req.path, req.method);
      if (!resourceInfo) return;

      const duration = Date.now() - startTime;
      const requestData = captureRequestData(req);
      
      // Parse response data if it's a string
      let parsedResponseData = null;
      if (responseData && typeof responseData === 'string') {
        try {
          parsedResponseData = JSON.parse(responseData);
        } catch {
          parsedResponseData = { message: responseData };
        }
      } else if (responseData) {
        parsedResponseData = responseData;
      }

      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: resourceInfo.action,
          resource: resourceInfo.resource,
          resourceId: resourceInfo.resourceId,
          oldValues: req.method === 'PUT' || req.method === 'PATCH' ? requestData : null,
          newValues: res.statusCode < 400 ? parsedResponseData : null,
          ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
        },
      });

      // Log to application logger as well
      logger.info('Audit Log', {
        userId: req.user.id,
        username: req.user.username,
        action: resourceInfo.action,
        resource: resourceInfo.resource,
        resourceId: resourceInfo.resourceId,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

    } catch (error) {
      // Don't fail the request if audit logging fails
      logger.error('Audit logging failed:', error);
    }
  });
};

// Manual audit logging function for complex operations
export const createAuditLog = async (
  userId: string,
  action: string,
  resource: string,
  resourceId?: string,
  oldValues?: any,
  newValues?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<void> => {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        resource,
        resourceId,
        oldValues,
        newValues,
        ipAddress: ipAddress || 'system',
        userAgent: userAgent || 'system',
      },
    });
  } catch (error) {
    logger.error('Manual audit logging failed:', error);
  }
};
