"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.asyncHandler = exports.InternalServerError = exports.ConflictError = exports.ForbiddenError = exports.UnauthorizedError = exports.NotFoundError = exports.ValidationError = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.statusCode = 400;
        this.isOperational = true;
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class NotFoundError extends Error {
    constructor(message = 'Resource not found') {
        super(message);
        this.statusCode = 404;
        this.isOperational = true;
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class UnauthorizedError extends Error {
    constructor(message = 'Unauthorized') {
        super(message);
        this.statusCode = 401;
        this.isOperational = true;
        this.name = 'UnauthorizedError';
    }
}
exports.UnauthorizedError = UnauthorizedError;
class ForbiddenError extends Error {
    constructor(message = 'Forbidden') {
        super(message);
        this.statusCode = 403;
        this.isOperational = true;
        this.name = 'ForbiddenError';
    }
}
exports.ForbiddenError = ForbiddenError;
class ConflictError extends Error {
    constructor(message) {
        super(message);
        this.statusCode = 409;
        this.isOperational = true;
        this.name = 'ConflictError';
    }
}
exports.ConflictError = ConflictError;
class InternalServerError extends Error {
    constructor(message = 'Internal server error') {
        super(message);
        this.statusCode = 500;
        this.isOperational = true;
        this.name = 'InternalServerError';
    }
}
exports.InternalServerError = InternalServerError;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const handlePrismaError = (error) => {
    let message = 'Database error';
    let statusCode = 500;
    switch (error.code) {
        case 'P2002':
            const target = error.meta?.target;
            message = `Duplicate value for ${target?.join(', ') || 'field'}`;
            statusCode = 409;
            break;
        case 'P2014':
            message = 'Invalid ID provided';
            statusCode = 400;
            break;
        case 'P2003':
            message = 'Referenced record does not exist';
            statusCode = 400;
            break;
        case 'P2025':
            message = 'Record not found';
            statusCode = 404;
            break;
        case 'P2016':
            message = 'Invalid query parameters';
            statusCode = 400;
            break;
        default:
            message = 'Database operation failed';
            statusCode = 500;
    }
    const appError = new Error(message);
    appError.statusCode = statusCode;
    appError.isOperational = true;
    return appError;
};
const errorHandler = (error, req, res, _next) => {
    let err = error;
    if (error instanceof client_1.Prisma.PrismaClientKnownRequestError) {
        err = handlePrismaError(error);
    }
    else if (error instanceof client_1.Prisma.PrismaClientValidationError) {
        err = new ValidationError('Invalid data provided');
    }
    else if (error instanceof client_1.Prisma.PrismaClientUnknownRequestError) {
        err = new Error('Database connection error');
        err.statusCode = 500;
        err.isOperational = true;
    }
    err.statusCode = err.statusCode || 500;
    err.isOperational = err.isOperational || false;
    if (err.statusCode >= 500) {
        logger_1.logger.error('Server Error:', {
            message: err.message,
            stack: err.stack,
            url: req.url,
            method: req.method,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
        });
    }
    else {
        logger_1.logger.warn('Client Error:', {
            message: err.message,
            url: req.url,
            method: req.method,
            ip: req.ip,
            userId: req.user?.id,
        });
    }
    const response = {
        error: err.message,
        status: err.statusCode,
        timestamp: new Date().toISOString(),
    };
    if (process.env.NODE_ENV === 'development') {
        response.stack = err.stack;
        response.details = err;
    }
    if (req.headers['x-request-id']) {
        response.requestId = req.headers['x-request-id'];
    }
    res.status(err.statusCode).json(response);
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=errorHandler.js.map