# Database Configuration
DATABASE_URL="postgresql://military_user:military_password@localhost:5432/military_assets"

# Redis Configuration (Optional)
REDIS_URL="redis://localhost:6379"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="24h"

# Server Configuration
PORT=3001
NODE_ENV="development"

# Frontend Configuration
VITE_API_URL="http://localhost:3001/api"
VITE_APP_NAME="Military Asset Management System"

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# Email Configuration (for notifications)
SMTP_HOST=""
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM="<EMAIL>"

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH="uploads/"

# Audit Configuration
AUDIT_LOG_RETENTION_DAYS=2555  # 7 years
ENABLE_AUDIT_LOGGING=true
