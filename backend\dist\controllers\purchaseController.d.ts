import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const purchaseController: {
    getAllPurchases(req: Request, res: Response): Promise<void>;
    getPurchaseById(req: Request, res: Response): Promise<void>;
    createPurchase(req: AuthenticatedRequest, res: Response): Promise<void>;
    updatePurchase(req: Request, res: Response): Promise<void>;
    deletePurchase(req: Request, res: Response): Promise<void>;
};
//# sourceMappingURL=purchaseController.d.ts.map