import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const purchaseController: {
    getAllPurchases(req: Request, res: Response): Promise<void>;
    getPurchaseById(req: Request, res: Response): Promise<Response | void>;
    createPurchase(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    updatePurchase(req: Request, res: Response): Promise<Response | void>;
    deletePurchase(req: Request, res: Response): Promise<Response | void>;
};
//# sourceMappingURL=purchaseController.d.ts.map