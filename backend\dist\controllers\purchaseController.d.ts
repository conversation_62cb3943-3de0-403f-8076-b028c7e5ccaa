import { Request, Response } from 'express';
export declare const purchaseController: {
    getAllPurchases(req: Request, res: Response): Promise<void>;
    getPurchaseById(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    createPurchase(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    updatePurchase(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    deletePurchase(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
};
//# sourceMappingURL=purchaseController.d.ts.map