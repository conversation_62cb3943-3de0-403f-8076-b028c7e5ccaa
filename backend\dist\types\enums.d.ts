export declare const UserRole: {
    readonly ADMIN: "ADMIN";
    readonly BASE_COMMANDER: "BASE_COMMANDER";
    readonly LOGISTICS_OFFICER: "LOGISTICS_OFFICER";
};
export type UserRoleType = typeof UserRole[keyof typeof UserRole];
export declare const TransactionType: {
    readonly PURCHASE: "PURCHASE";
    readonly TRANSFER_IN: "TRANSFER_IN";
    readonly TRANSFER_OUT: "TRANSFER_OUT";
    readonly ASSIGNMENT: "ASSIGNMENT";
    readonly EXPENDITURE: "EXPENDITURE";
    readonly RETURN: "RETURN";
    readonly MAINTENANCE: "MAINTENANCE";
};
export type TransactionTypeType = typeof TransactionType[keyof typeof TransactionType];
export declare const TransferStatus: {
    readonly PENDING: "PENDING";
    readonly APPROVED: "APPROVED";
    readonly IN_TRANSIT: "IN_TRANSIT";
    readonly COMPLETED: "COMPLETED";
    readonly CANCELLED: "CANCELLED";
};
export type TransferStatusType = typeof TransferStatus[keyof typeof TransferStatus];
export declare const AssetStatus: {
    readonly AVAILABLE: "AVAILABLE";
    readonly ASSIGNED: "ASSIGNED";
    readonly IN_MAINTENANCE: "IN_MAINTENANCE";
    readonly DECOMMISSIONED: "DECOMMISSIONED";
    readonly LOST: "LOST";
};
export type AssetStatusType = typeof AssetStatus[keyof typeof AssetStatus];
export declare const EquipmentCategory: {
    readonly WEAPONS: "WEAPONS";
    readonly VEHICLES: "VEHICLES";
    readonly AMMUNITION: "AMMUNITION";
    readonly COMMUNICATION: "COMMUNICATION";
    readonly MEDICAL: "MEDICAL";
    readonly SUPPLIES: "SUPPLIES";
    readonly ELECTRONICS: "ELECTRONICS";
    readonly PROTECTIVE_GEAR: "PROTECTIVE_GEAR";
};
export type EquipmentCategoryType = typeof EquipmentCategory[keyof typeof EquipmentCategory];
//# sourceMappingURL=enums.d.ts.map