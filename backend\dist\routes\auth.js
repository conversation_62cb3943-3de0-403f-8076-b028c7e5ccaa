"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authController_1 = require("../controllers/authController");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const auth_1 = require("../middleware/auth");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
router.post('/login', (0, validation_1.validate)(validation_2.authSchemas.login), (0, errorHandler_1.asyncHandler)(authController_1.authController.login));
router.post('/register', (0, validation_1.validate)(validation_2.authSchemas.register), (0, errorHandler_1.asyncHandler)(authController_1.authController.register));
router.post('/refresh', (0, errorHandler_1.asyncHandler)(authController_1.authController.refreshToken));
router.post('/logout', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(authController_1.authController.logout));
router.get('/me', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(authController_1.authController.getCurrentUser));
router.put('/profile', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(authController_1.authController.updateProfile));
router.put('/password', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(authController_1.authController.changePassword));
exports.default = router;
//# sourceMappingURL=auth.js.map