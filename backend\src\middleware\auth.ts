import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient, UserRole } from '@prisma/client';
import { logger } from '../utils/logger';

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    username: string;
    role: UserRole;
    baseId?: string;
  };
}

// JWT Authentication middleware
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Access token required' });
      return;
    }

    const token = authHeader.substring(7);
    const jwtSecret = process.env.JWT_SECRET;

    if (!jwtSecret) {
      logger.error('JWT_SECRET not configured');
      res.status(500).json({ error: 'Server configuration error' });
      return;
    }

    const decoded = jwt.verify(token, jwtSecret) as { userId: string };
    
    // Fetch user from database to ensure they still exist and are active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        baseId: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      res.status(401).json({ error: 'Invalid or inactive user' });
      return;
    }

    // Update last login
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLogin: new Date() },
    });

    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({ error: 'Invalid token' });
      return;
    }
    
    logger.error('Authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

// Role-based authorization middleware
export const authorize = (allowedRoles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      res.status(403).json({ 
        error: 'Insufficient permissions',
        required: allowedRoles,
        current: req.user.role,
      });
      return;
    }

    next();
  };
};

// Base-specific authorization middleware
export const authorizeBase = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({ error: 'Authentication required' });
    return;
  }

  // Admin can access all bases
  if (req.user.role === UserRole.ADMIN) {
    next();
    return;
  }

  // Extract baseId from request (params, query, or body)
  const requestedBaseId = req.params.baseId || req.query.baseId || req.body.baseId;

  if (!requestedBaseId) {
    res.status(400).json({ error: 'Base ID required' });
    return;
  }

  // Base commanders and logistics officers can only access their assigned base
  if (req.user.baseId !== requestedBaseId) {
    res.status(403).json({ 
      error: 'Access denied to this base',
      userBase: req.user.baseId,
      requestedBase: requestedBaseId,
    });
    return;
  }

  next();
};

// Resource ownership authorization
export const authorizeResourceOwnership = (resourceType: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    // Admin can access all resources
    if (req.user.role === UserRole.ADMIN) {
      next();
      return;
    }

    try {
      const resourceId = req.params.id;
      if (!resourceId) {
        res.status(400).json({ error: 'Resource ID required' });
        return;
      }

      let resource;
      
      switch (resourceType) {
        case 'purchase':
          resource = await prisma.purchase.findUnique({
            where: { id: resourceId },
            select: { baseId: true },
          });
          break;
        case 'transfer':
          resource = await prisma.transfer.findUnique({
            where: { id: resourceId },
            select: { fromBaseId: true, toBaseId: true },
          });
          break;
        case 'assignment':
          resource = await prisma.assetAssignment.findUnique({
            where: { id: resourceId },
            select: { baseId: true },
          });
          break;
        default:
          res.status(400).json({ error: 'Invalid resource type' });
          return;
      }

      if (!resource) {
        res.status(404).json({ error: 'Resource not found' });
        return;
      }

      // Check if user has access to the resource's base
      const hasAccess = resourceType === 'transfer' 
        ? (resource as any).fromBaseId === req.user.baseId || (resource as any).toBaseId === req.user.baseId
        : (resource as any).baseId === req.user.baseId;

      if (!hasAccess) {
        res.status(403).json({ error: 'Access denied to this resource' });
        return;
      }

      next();
    } catch (error) {
      logger.error(`Authorization error for ${resourceType}:`, error);
      res.status(500).json({ error: 'Authorization failed' });
    }
  };
};
