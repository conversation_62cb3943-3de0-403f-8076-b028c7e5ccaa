{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,qBAAa,eAAgB,SAAQ,KAAK;IACxC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,aAAc,SAAQ,KAAK;IACtC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAA6B;CAInD;AAED,qBAAa,iBAAkB,SAAQ,KAAK;IAC1C,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAAuB;CAI7C;AAED,qBAAa,cAAe,SAAQ,KAAK;IACvC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAAoB;CAI1C;AAED,qBAAa,aAAc,SAAQ,KAAK;IACtC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,mBAAoB,SAAQ,KAAK;IAC5C,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAAgC;CAItD;AAGD,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAgDF,eAAO,MAAM,YAAY,GACvB,OAAO,KAAK,GAAG,QAAQ,EACvB,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,OAAO,YAAY,KAClB,IA0DF,CAAC"}