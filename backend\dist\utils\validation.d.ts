import Joi from 'joi';
export declare const commonSchemas: {
    id: Joi.StringSchema<string>;
    email: Joi.StringSchema<string>;
    password: Joi.StringSchema<string>;
    positiveInteger: Joi.NumberSchema<number>;
    nonNegativeInteger: Joi.NumberSchema<number>;
    price: Joi.NumberSchema<number>;
    date: Joi.DateSchema<Date>;
};
export declare const authSchemas: {
    login: Joi.ObjectSchema<any>;
    register: Joi.ObjectSchema<any>;
};
export declare const assetSchemas: {
    create: Joi.ObjectSchema<any>;
    update: Joi.ObjectSchema<any>;
};
export declare const purchaseSchemas: {
    create: Joi.ObjectSchema<any>;
};
export declare const transferSchemas: {
    create: Joi.ObjectSchema<any>;
    updateStatus: Joi.ObjectSchema<any>;
};
export declare const assignmentSchemas: {
    create: Joi.ObjectSchema<any>;
    return: Joi.ObjectSchema<any>;
};
export declare const querySchemas: {
    pagination: Joi.ObjectSchema<any>;
    dateRange: Joi.ObjectSchema<any>;
    baseFilter: Joi.ObjectSchema<any>;
    assetFilter: Joi.ObjectSchema<any>;
};
export declare const validate: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
export declare const validateQuery: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => any;
export declare const expenditureSchemas: {
    create: Joi.ObjectSchema<any>;
    update: Joi.ObjectSchema<any>;
};
//# sourceMappingURL=validation.d.ts.map