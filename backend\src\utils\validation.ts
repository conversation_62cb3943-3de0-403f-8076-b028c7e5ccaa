import Joi from 'joi';
import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';

// Common validation schemas
export const commonSchemas = {
  id: Joi.string().required().messages({
    'string.empty': 'ID is required',
    'any.required': 'ID is required',
  }),
  
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'string.empty': 'Email is required',
    'any.required': 'Email is required',
  }),
  
  password: Joi.string().min(8).required().messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.empty': 'Password is required',
    'any.required': 'Password is required',
  }),
  
  positiveInteger: Joi.number().integer().min(1).required().messages({
    'number.base': 'Must be a number',
    'number.integer': 'Must be an integer',
    'number.min': 'Must be greater than 0',
    'any.required': 'This field is required',
  }),
  
  nonNegativeInteger: Joi.number().integer().min(0).required().messages({
    'number.base': 'Must be a number',
    'number.integer': 'Must be an integer',
    'number.min': 'Must be 0 or greater',
    'any.required': 'This field is required',
  }),
  
  price: Joi.number().precision(2).min(0).messages({
    'number.base': 'Must be a number',
    'number.precision': 'Maximum 2 decimal places allowed',
    'number.min': 'Must be 0 or greater',
  }),
  
  date: Joi.date().iso().messages({
    'date.base': 'Must be a valid date',
    'date.format': 'Date must be in ISO format',
  }),
};

// Authentication schemas
export const authSchemas = {
  login: Joi.object({
    email: commonSchemas.email,
    password: Joi.string().required().messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required',
    }),
  }),
  
  register: Joi.object({
    email: commonSchemas.email,
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      'string.alphanum': 'Username must contain only letters and numbers',
      'string.min': 'Username must be at least 3 characters long',
      'string.max': 'Username must not exceed 30 characters',
      'string.empty': 'Username is required',
      'any.required': 'Username is required',
    }),
    firstName: Joi.string().min(1).max(50).required().messages({
      'string.min': 'First name is required',
      'string.max': 'First name must not exceed 50 characters',
      'string.empty': 'First name is required',
      'any.required': 'First name is required',
    }),
    lastName: Joi.string().min(1).max(50).required().messages({
      'string.min': 'Last name is required',
      'string.max': 'Last name must not exceed 50 characters',
      'string.empty': 'Last name is required',
      'any.required': 'Last name is required',
    }),
    password: commonSchemas.password,
    role: Joi.string().valid(...Object.values(UserRole)).required().messages({
      'any.only': 'Invalid role specified',
      'any.required': 'Role is required',
    }),
    baseId: Joi.string().when('role', {
      is: Joi.string().valid(UserRole.BASE_COMMANDER, UserRole.LOGISTICS_OFFICER),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }).messages({
      'any.required': 'Base assignment is required for this role',
    }),
  }),
};

// Asset schemas
export const assetSchemas = {
  create: Joi.object({
    name: Joi.string().min(1).max(100).required().messages({
      'string.min': 'Asset name is required',
      'string.max': 'Asset name must not exceed 100 characters',
      'string.empty': 'Asset name is required',
      'any.required': 'Asset name is required',
    }),
    code: Joi.string().alphanum().min(3).max(20).required().messages({
      'string.alphanum': 'Asset code must contain only letters and numbers',
      'string.min': 'Asset code must be at least 3 characters long',
      'string.max': 'Asset code must not exceed 20 characters',
      'string.empty': 'Asset code is required',
      'any.required': 'Asset code is required',
    }),
    category: Joi.string().valid(...Object.values(EquipmentCategory)).required().messages({
      'any.only': 'Invalid equipment category',
      'any.required': 'Equipment category is required',
    }),
    description: Joi.string().max(500).optional().messages({
      'string.max': 'Description must not exceed 500 characters',
    }),
    unitPrice: commonSchemas.price.optional(),
  }),
  
  update: Joi.object({
    name: Joi.string().min(1).max(100).optional(),
    description: Joi.string().max(500).optional(),
    unitPrice: commonSchemas.price.optional(),
    isActive: Joi.boolean().optional(),
  }),
};

// Purchase schemas
export const purchaseSchemas = {
  create: Joi.object({
    baseId: commonSchemas.id,
    vendor: Joi.string().min(1).max(100).required().messages({
      'string.min': 'Vendor name is required',
      'string.max': 'Vendor name must not exceed 100 characters',
      'string.empty': 'Vendor name is required',
      'any.required': 'Vendor name is required',
    }),
    description: Joi.string().max(500).optional(),
    items: Joi.array().items(
      Joi.object({
        assetId: commonSchemas.id,
        quantity: commonSchemas.positiveInteger,
        unitPrice: commonSchemas.price.required(),
      })
    ).min(1).required().messages({
      'array.min': 'At least one item is required',
      'any.required': 'Purchase items are required',
    }),
  }),
};

// Transfer schemas
export const transferSchemas = {
  create: Joi.object({
    fromBaseId: commonSchemas.id,
    toBaseId: commonSchemas.id.invalid(Joi.ref('fromBaseId')).messages({
      'any.invalid': 'Destination base must be different from source base',
    }),
    description: Joi.string().max(500).optional(),
    items: Joi.array().items(
      Joi.object({
        assetId: commonSchemas.id,
        quantity: commonSchemas.positiveInteger,
      })
    ).min(1).required().messages({
      'array.min': 'At least one item is required',
      'any.required': 'Transfer items are required',
    }),
  }),
  
  updateStatus: Joi.object({
    status: Joi.string().valid(...Object.values(TransferStatus)).required().messages({
      'any.only': 'Invalid transfer status',
      'any.required': 'Transfer status is required',
    }),
  }),
};

// Assignment schemas
export const assignmentSchemas = {
  create: Joi.object({
    baseId: commonSchemas.id,
    assetId: commonSchemas.id,
    assignedTo: commonSchemas.id,
    quantity: commonSchemas.positiveInteger,
    description: Joi.string().max(500).optional(),
  }),
  
  return: Joi.object({
    quantity: commonSchemas.positiveInteger.optional(),
    description: Joi.string().max(500).optional(),
  }),
};

// Query parameter schemas
export const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
  }),
  
  dateRange: Joi.object({
    startDate: commonSchemas.date.optional(),
    endDate: commonSchemas.date.optional().when('startDate', {
      is: Joi.exist(),
      then: Joi.date().min(Joi.ref('startDate')),
    }),
  }),
  
  baseFilter: Joi.object({
    baseId: commonSchemas.id.optional(),
  }),
  
  assetFilter: Joi.object({
    category: Joi.string().valid(...Object.values(EquipmentCategory)).optional(),
    assetId: commonSchemas.id.optional(),
  }),
};

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      
      return res.status(400).json({
        error: 'Validation failed',
        details: errors,
      });
    }
    
    req.body = value;
    next();
  };
};

// Query validation middleware factory
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      
      return res.status(400).json({
        error: 'Query validation failed',
        details: errors,
      });
    }
    
    req.query = value;
    next();
  };
};

// Expenditure schemas
export const expenditureSchemas = {
  create: Joi.object({
    baseId: commonSchemas.id,
    assetId: commonSchemas.id,
    quantity: commonSchemas.positiveInteger,
    type: Joi.string().valid('EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL').required(),
    unitPrice: Joi.number().positive().optional(),
    description: Joi.string().max(1000).optional(),
    reason: Joi.string().max(500).optional(),
  }),
  update: Joi.object({
    description: Joi.string().max(1000).optional(),
    unitPrice: Joi.number().positive().optional(),
  }),
};
