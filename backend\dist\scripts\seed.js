"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma = new client_1.PrismaClient();
async function main() {
    console.log('🌱 Starting database seeding...');
    const bases = await Promise.all([
        prisma.base.create({
            data: {
                name: 'Fort Alpha',
                code: 'FA001',
                location: 'Northern Region',
            },
        }),
        prisma.base.create({
            data: {
                name: 'Base Bravo',
                code: 'BB002',
                location: 'Central Region',
            },
        }),
        prisma.base.create({
            data: {
                name: 'Camp Charlie',
                code: 'CC003',
                location: 'Southern Region',
            },
        }),
    ]);
    console.log('✅ Created bases');
    const hashedPassword = await bcryptjs_1.default.hash('password123', 12);
    const users = await Promise.all([
        prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'admin',
                firstName: 'System',
                lastName: 'Administrator',
                password: hashedPassword,
                role: enums_1.UserRole.ADMIN,
            },
        }),
        prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'cmd_alpha',
                firstName: 'John',
                lastName: 'Smith',
                password: hashedPassword,
                role: enums_1.UserRole.BASE_COMMANDER,
                baseId: bases[0].id,
            },
        }),
        prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'cmd_bravo',
                firstName: 'Jane',
                lastName: 'Johnson',
                password: hashedPassword,
                role: enums_1.UserRole.BASE_COMMANDER,
                baseId: bases[1].id,
            },
        }),
        prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'log_alpha',
                firstName: 'Mike',
                lastName: 'Wilson',
                password: hashedPassword,
                role: enums_1.UserRole.LOGISTICS_OFFICER,
                baseId: bases[0].id,
            },
        }),
        prisma.user.create({
            data: {
                email: '<EMAIL>',
                username: 'log_bravo',
                firstName: 'Sarah',
                lastName: 'Davis',
                password: hashedPassword,
                role: enums_1.UserRole.LOGISTICS_OFFICER,
                baseId: bases[1].id,
            },
        }),
    ]);
    console.log('✅ Created users');
    await prisma.base.update({
        where: { id: bases[0].id },
        data: { commanderId: users[1].id },
    });
    await prisma.base.update({
        where: { id: bases[1].id },
        data: { commanderId: users[2].id },
    });
    console.log('✅ Assigned commanders to bases');
    const assets = await Promise.all([
        prisma.asset.create({
            data: {
                name: 'M4A1 Carbine',
                code: 'WPN001',
                category: enums_1.EquipmentCategory.WEAPONS,
                description: 'Standard issue assault rifle',
                unitPrice: 1200.00,
            },
        }),
        prisma.asset.create({
            data: {
                name: 'M9 Pistol',
                code: 'WPN002',
                category: enums_1.EquipmentCategory.WEAPONS,
                description: 'Standard issue sidearm',
                unitPrice: 600.00,
            },
        }),
        prisma.asset.create({
            data: {
                name: 'HMMWV',
                code: 'VEH001',
                category: enums_1.EquipmentCategory.VEHICLES,
                description: 'High Mobility Multipurpose Wheeled Vehicle',
                unitPrice: 85000.00,
            },
        }),
        prisma.asset.create({
            data: {
                name: 'M1A2 Abrams Tank',
                code: 'VEH002',
                category: enums_1.EquipmentCategory.VEHICLES,
                description: 'Main battle tank',
                unitPrice: 6200000.00,
            },
        }),
        prisma.asset.create({
            data: {
                name: '5.56mm NATO Rounds',
                code: 'AMM001',
                category: enums_1.EquipmentCategory.AMMUNITION,
                description: 'Standard rifle ammunition',
                unitPrice: 0.75,
            },
        }),
        prisma.asset.create({
            data: {
                name: 'AN/PRC-152 Radio',
                code: 'COM001',
                category: enums_1.EquipmentCategory.COMMUNICATION,
                description: 'Tactical radio system',
                unitPrice: 4500.00,
            },
        }),
        prisma.asset.create({
            data: {
                name: 'Body Armor Vest',
                code: 'PRO001',
                category: enums_1.EquipmentCategory.PROTECTIVE_GEAR,
                description: 'Level IIIA ballistic vest',
                unitPrice: 800.00,
            },
        }),
    ]);
    console.log('✅ Created assets');
    const inventoryData = [
        { baseId: bases[0].id, assetId: assets[0].id, openingBalance: 50, currentBalance: 45, assignedCount: 5, availableCount: 40 },
        { baseId: bases[0].id, assetId: assets[1].id, openingBalance: 30, currentBalance: 28, assignedCount: 2, availableCount: 26 },
        { baseId: bases[0].id, assetId: assets[2].id, openingBalance: 10, currentBalance: 8, assignedCount: 2, availableCount: 6 },
        { baseId: bases[0].id, assetId: assets[4].id, openingBalance: 10000, currentBalance: 8500, assignedCount: 0, availableCount: 8500 },
        { baseId: bases[0].id, assetId: assets[5].id, openingBalance: 15, currentBalance: 12, assignedCount: 3, availableCount: 9 },
        { baseId: bases[0].id, assetId: assets[6].id, openingBalance: 100, currentBalance: 85, assignedCount: 15, availableCount: 70 },
        { baseId: bases[1].id, assetId: assets[0].id, openingBalance: 40, currentBalance: 35, assignedCount: 5, availableCount: 30 },
        { baseId: bases[1].id, assetId: assets[1].id, openingBalance: 25, currentBalance: 22, assignedCount: 3, availableCount: 19 },
        { baseId: bases[1].id, assetId: assets[2].id, openingBalance: 8, currentBalance: 6, assignedCount: 2, availableCount: 4 },
        { baseId: bases[1].id, assetId: assets[3].id, openingBalance: 2, currentBalance: 2, assignedCount: 0, availableCount: 2 },
        { baseId: bases[1].id, assetId: assets[4].id, openingBalance: 8000, currentBalance: 7200, assignedCount: 0, availableCount: 7200 },
        { baseId: bases[1].id, assetId: assets[5].id, openingBalance: 12, currentBalance: 10, assignedCount: 2, availableCount: 8 },
        { baseId: bases[2].id, assetId: assets[0].id, openingBalance: 35, currentBalance: 30, assignedCount: 5, availableCount: 25 },
        { baseId: bases[2].id, assetId: assets[1].id, openingBalance: 20, currentBalance: 18, assignedCount: 2, availableCount: 16 },
        { baseId: bases[2].id, assetId: assets[2].id, openingBalance: 6, currentBalance: 5, assignedCount: 1, availableCount: 4 },
        { baseId: bases[2].id, assetId: assets[4].id, openingBalance: 6000, currentBalance: 5500, assignedCount: 0, availableCount: 5500 },
        { baseId: bases[2].id, assetId: assets[6].id, openingBalance: 80, currentBalance: 70, assignedCount: 10, availableCount: 60 },
    ];
    await Promise.all(inventoryData.map(data => prisma.inventory.create({ data })));
    console.log('✅ Created initial inventory');
    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Created ${bases.length} military bases`);
    console.log(`- Created ${users.length} users (1 admin, 2 commanders, 2 logistics officers)`);
    console.log(`- Created ${assets.length} asset types`);
    console.log(`- Created ${inventoryData.length} inventory records`);
    console.log('\n🔐 Default login credentials:');
    console.log('Admin: <EMAIL> / password123');
    console.log('Commander (Alpha): <EMAIL> / password123');
    console.log('Logistics (Alpha): <EMAIL> / password123');
}
main()
    .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
//# sourceMappingURL=seed.js.map