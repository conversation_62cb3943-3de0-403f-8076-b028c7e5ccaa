import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../middleware/auth';
export declare const authController: {
    login: (req: Request, res: Response) => Promise<void>;
    register: (req: Request, res: Response) => Promise<void>;
    refreshToken: (req: Request, res: Response) => Promise<void>;
    logout: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getCurrentUser: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    updateProfile: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    changePassword: (req: AuthenticatedRequest, res: Response) => Promise<void>;
};
//# sourceMappingURL=authController.d.ts.map