{"version": 3, "file": "auditLogger.js", "sourceRoot": "", "sources": ["../../src/middleware/auditLogger.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAE9C,4CAAyC;AAEzC,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAGlC,MAAM,iBAAiB,GAAG;IACxB,MAAM;IACN,KAAK;IACL,OAAO;IACP,QAAQ;CACT,CAAC;AAGF,MAAM,mBAAmB,GAAG;IAC1B,gBAAgB;IAChB,gBAAgB;IAChB,kBAAkB;IAClB,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,YAAY;CACb,CAAC;AAGF,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,MAAc,EAAE,EAAE;IAC1D,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAEhD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC;QAAE,OAAO,IAAI,CAAC;IAErC,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7B,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjC,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE/B,OAAO;QACL,QAAQ,EAAE,YAAY;QACtB,UAAU,EAAE,UAAU,IAAI,IAAI;QAC9B,MAAM,EAAE,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,CAAC;KAClD,CAAC;AACJ,CAAC,CAAC;AAGF,MAAM,mBAAmB,GAAG,CAAC,MAAc,EAAE,KAAc,EAAU,EAAE;IACrE,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK,CAAC;QACX,KAAK,OAAO;YACV,OAAO,QAAQ,CAAC;QAClB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,KAAK;YACR,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QACjC;YACE,OAAO,MAAM,CAAC;IAClB,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,kBAAkB,GAAG,CAAC,GAAY,EAAO,EAAE;IAC/C,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACxD,MAAM,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAG7B,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACpD,CAAC,CAAC;AAGK,MAAM,WAAW,GAAG,KAAK,EAC9B,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IAEjB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACvC,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QACzE,IAAI,EAAE,CAAC;QACP,OAAO;IACT,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/D,IAAI,EAAE,CAAC;QACP,OAAO;IACT,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,IAAI,YAAY,GAAQ,IAAI,CAAC;IAG7B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,YAAY,GAAG,IAAI,CAAC;QACpB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAGF,IAAI,EAAE,CAAC;IAGP,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC1B,IAAI,CAAC;YAEH,IAAI,CAAC,GAAG,CAAC,IAAI;gBAAE,OAAO;YAEtB,MAAM,YAAY,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;YAC/D,IAAI,CAAC,YAAY;gBAAE,OAAO;YAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,WAAW,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAG5C,IAAI,kBAAkB,GAAG,IAAI,CAAC;YAC9B,IAAI,YAAY,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrD,IAAI,CAAC;oBACH,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD,CAAC;gBAAC,MAAM,CAAC;oBACP,kBAAkB,GAAG,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;gBACjD,CAAC;YACH,CAAC;iBAAM,IAAI,YAAY,EAAE,CAAC;gBACxB,kBAAkB,GAAG,YAAY,CAAC;YACpC,CAAC;YAGD,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE;oBACJ,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;oBACnB,MAAM,EAAE,YAAY,CAAC,MAAM;oBAC3B,QAAQ,EAAE,YAAY,CAAC,QAAQ;oBAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;oBACnC,SAAS,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;oBAC9E,SAAS,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI;oBAC3D,SAAS,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,SAAS;oBAC9D,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS;iBAC9C;aACF,CAAC,CAAC;YAGH,eAAM,CAAC,IAAI,CAAC,WAAW,EAAE;gBACvB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;gBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;gBAC3B,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,QAAQ,EAAE,YAAY,CAAC,QAAQ;gBAC/B,UAAU,EAAE,YAAY,CAAC,UAAU;gBACnC,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,UAAU,EAAE,GAAG,CAAC,UAAU;gBAC1B,QAAQ;gBACR,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;aACjC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAzFW,QAAA,WAAW,eAyFtB;AAGK,MAAM,cAAc,GAAG,KAAK,EACjC,MAAc,EACd,MAAc,EACd,QAAgB,EAChB,UAAmB,EACnB,SAAe,EACf,SAAe,EACf,SAAkB,EAClB,SAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,IAAI,EAAE;gBACJ,MAAM;gBACN,MAAM;gBACN,QAAQ;gBACR,UAAU,EAAE,UAAU,IAAI,IAAI;gBAC9B,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,SAAS,IAAI,QAAQ;gBAChC,SAAS,EAAE,SAAS,IAAI,QAAQ;aACjC;SACF,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;AACH,CAAC,CAAC;AA1BW,QAAA,cAAc,kBA0BzB"}