"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.purchaseController = void 0;
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const prisma = new client_1.PrismaClient();
exports.purchaseController = {
    async getAllPurchases(req, res) {
        try {
            const { page = 1, limit = 10, baseId, startDate, endDate } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const where = {};
            if (baseId) {
                where.baseId = baseId;
            }
            if (startDate || endDate) {
                where.createdAt = {};
                if (startDate)
                    where.createdAt.gte = new Date(startDate);
                if (endDate)
                    where.createdAt.lte = new Date(endDate);
            }
            const [purchases, total] = await Promise.all([
                prisma.purchase.findMany({
                    where,
                    skip,
                    take: Number(limit),
                    include: {
                        base: {
                            select: {
                                id: true,
                                name: true,
                                code: true,
                                location: true,
                            },
                        },
                        createdBy: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                            },
                        },
                        items: {
                            include: {
                                asset: {
                                    select: {
                                        id: true,
                                        name: true,
                                        code: true,
                                        category: true,
                                        unitPrice: true,
                                    },
                                },
                            },
                        },
                    },
                    orderBy: { createdAt: 'desc' },
                }),
                prisma.purchase.count({ where }),
            ]);
            res.json({
                purchases,
                pagination: {
                    page: Number(page),
                    limit: Number(limit),
                    total,
                    pages: Math.ceil(total / Number(limit)),
                },
            });
        }
        catch (error) {
            console.error('Error fetching purchases:', error);
            res.status(500).json({ error: 'Failed to fetch purchases' });
        }
    },
    async getPurchaseById(req, res) {
        try {
            const { id } = req.params;
            const purchase = await prisma.purchase.findUnique({
                where: { id },
                include: {
                    base: {
                        select: {
                            id: true,
                            name: true,
                            code: true,
                            location: true,
                        },
                    },
                    createdBy: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    items: {
                        include: {
                            asset: {
                                select: {
                                    id: true,
                                    name: true,
                                    code: true,
                                    category: true,
                                    unitPrice: true,
                                },
                            },
                        },
                    },
                },
            });
            if (!purchase) {
                return res.status(404).json({ error: 'Purchase not found' });
            }
            res.json({ purchase });
        }
        catch (error) {
            console.error('Error fetching purchase:', error);
            res.status(500).json({ error: 'Failed to fetch purchase' });
        }
    },
    async createPurchase(req, res) {
        try {
            const { baseId, vendor, description, items } = req.body;
            const userId = req.user?.id;
            if (!userId) {
                return res.status(401).json({ error: 'User not authenticated' });
            }
            const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
            const result = await prisma.$transaction(async (tx) => {
                const purchase = await tx.purchase.create({
                    data: {
                        baseId,
                        vendor,
                        description: description || null,
                        totalAmount,
                        createdById: userId,
                    },
                });
                await Promise.all(items.map(item => tx.purchaseItem.create({
                    data: {
                        purchaseId: purchase.id,
                        assetId: item.assetId,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        totalPrice: item.quantity * item.unitPrice,
                    },
                })));
                for (const item of items) {
                    await tx.inventory.upsert({
                        where: {
                            baseId_assetId: {
                                baseId,
                                assetId: item.assetId,
                            },
                        },
                        update: {
                            currentBalance: {
                                increment: item.quantity,
                            },
                            availableCount: {
                                increment: item.quantity,
                            },
                        },
                        create: {
                            baseId,
                            assetId: item.assetId,
                            openingBalance: item.quantity,
                            currentBalance: item.quantity,
                            assignedCount: 0,
                            availableCount: item.quantity,
                        },
                    });
                    await tx.transaction.create({
                        data: {
                            type: enums_1.TransactionType.PURCHASE,
                            assetId: item.assetId,
                            baseId,
                            quantity: item.quantity,
                            totalValue: item.quantity * item.unitPrice,
                            referenceId: purchase.id,
                            description: `Purchase from ${vendor}`,
                            createdById: userId,
                        },
                    });
                }
                return purchase;
            });
            const completePurchase = await prisma.purchase.findUnique({
                where: { id: result.id },
                include: {
                    base: {
                        select: {
                            id: true,
                            name: true,
                            code: true,
                            location: true,
                        },
                    },
                    createdBy: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    items: {
                        include: {
                            asset: {
                                select: {
                                    id: true,
                                    name: true,
                                    code: true,
                                    category: true,
                                    unitPrice: true,
                                },
                            },
                        },
                    },
                },
            });
            res.status(201).json({ purchase: completePurchase });
        }
        catch (error) {
            console.error('Error creating purchase:', error);
            res.status(500).json({ error: 'Failed to create purchase' });
        }
    },
    async updatePurchase(req, res) {
        try {
            const { id } = req.params;
            const { vendor, description, items } = req.body;
            const existingPurchase = await prisma.purchase.findUnique({
                where: { id },
                include: { items: true },
            });
            if (!existingPurchase) {
                return res.status(404).json({ error: 'Purchase not found' });
            }
            const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
            const result = await prisma.$transaction(async (tx) => {
                const purchase = await tx.purchase.update({
                    where: { id },
                    data: {
                        vendor,
                        description: description || null,
                        totalAmount,
                    },
                });
                await tx.purchaseItem.deleteMany({
                    where: { purchaseId: id },
                });
                await Promise.all(items.map(item => tx.purchaseItem.create({
                    data: {
                        purchaseId: id,
                        assetId: item.assetId,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        totalPrice: item.quantity * item.unitPrice,
                    },
                })));
                return purchase;
            });
            const updatedPurchase = await prisma.purchase.findUnique({
                where: { id },
                include: {
                    base: {
                        select: {
                            id: true,
                            name: true,
                            code: true,
                            location: true,
                        },
                    },
                    createdBy: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            email: true,
                        },
                    },
                    items: {
                        include: {
                            asset: {
                                select: {
                                    id: true,
                                    name: true,
                                    code: true,
                                    category: true,
                                    unitPrice: true,
                                },
                            },
                        },
                    },
                },
            });
            res.json({ purchase: updatedPurchase });
        }
        catch (error) {
            console.error('Error updating purchase:', error);
            res.status(500).json({ error: 'Failed to update purchase' });
        }
    },
    async deletePurchase(req, res) {
        try {
            const { id } = req.params;
            const existingPurchase = await prisma.purchase.findUnique({
                where: { id },
                include: { items: true },
            });
            if (!existingPurchase) {
                return res.status(404).json({ error: 'Purchase not found' });
            }
            await prisma.$transaction(async (tx) => {
                for (const item of existingPurchase.items) {
                    await tx.inventory.update({
                        where: {
                            baseId_assetId: {
                                baseId: existingPurchase.baseId,
                                assetId: item.assetId,
                            },
                        },
                        data: {
                            currentBalance: {
                                decrement: item.quantity,
                            },
                            availableCount: {
                                decrement: item.quantity,
                            },
                        },
                    });
                }
                await tx.transaction.deleteMany({
                    where: {
                        referenceId: id,
                        type: enums_1.TransactionType.PURCHASE,
                    },
                });
                await tx.purchaseItem.deleteMany({
                    where: { purchaseId: id },
                });
                await tx.purchase.delete({
                    where: { id },
                });
            });
            res.json({ message: 'Purchase deleted successfully' });
        }
        catch (error) {
            console.error('Error deleting purchase:', error);
            res.status(500).json({ error: 'Failed to delete purchase' });
        }
    },
};
//# sourceMappingURL=purchaseController.js.map