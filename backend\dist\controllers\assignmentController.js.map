{"version": 3, "file": "assignmentController.js", "sourceRoot": "", "sources": ["../../src/controllers/assignmentController.ts"], "names": [], "mappings": ";;;AACA,2CAA8C;AAC9C,0CAA0C;AAE1C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAUrB,QAAA,oBAAoB,GAAG;IAElC,KAAK,CAAC,MAAM,CAAC,GAAyB,EAAE,GAAa;QACnD,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,GAAG,CAAC,EACR,KAAK,GAAG,EAAE,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,OAAO,GACR,GAAG,GAAG,CAAC,KAAK,CAAC;YAEd,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;YAG3B,IAAI,WAAW,GAAQ,EAAE,CAAC;YAG1B,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,cAAc,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,iBAAiB,EAAE,CAAC;gBAChG,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;YACvC,CAAC;YAGD,IAAI,MAAM;gBAAE,WAAW,CAAC,MAAM,GAAG,MAAgB,CAAC;YAClD,IAAI,OAAO;gBAAE,WAAW,CAAC,OAAO,GAAG,OAAiB,CAAC;YACrD,IAAI,MAAM;gBAAE,WAAW,CAAC,MAAM,GAAG,MAAgB,CAAC;YAClD,IAAI,UAAU;gBAAE,WAAW,CAAC,UAAU,GAAG,UAAoB,CAAC;YAG9D,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;gBACzB,WAAW,CAAC,UAAU,GAAG,EAAE,CAAC;gBAC5B,IAAI,SAAS;oBAAE,WAAW,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAmB,CAAC,CAAC;gBAC1E,IAAI,OAAO;oBAAE,WAAW,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAiB,CAAC,CAAC;YACxE,CAAC;YAED,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC7C,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC;oBAC9B,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;yBAC7C;wBACD,KAAK,EAAE;4BACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;yBAC7D;wBACD,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;yBAC9C;qBACF;oBACD,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;oBAC/B,IAAI;oBACJ,IAAI;iBACL,CAAC;gBACF,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;aACrD,CAAC,CAAC;YAEH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;YAE3C,GAAG,CAAC,IAAI,CAAC;gBACP,WAAW;gBACX,UAAU,EAAE;oBACV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;oBAClB,KAAK,EAAE,IAAI;oBACX,KAAK;oBACL,KAAK,EAAE,UAAU;iBAClB;aACF,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,OAAO,CAAC,GAAyB,EAAE,GAAa;QACpD,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAE1B,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,OAAO,EAAE;oBACP,IAAI,EAAE;wBACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;qBAC7C;oBACD,KAAK,EAAE;wBACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;qBAChF;oBACD,IAAI,EAAE;wBACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;qBAC9C;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACjE,CAAC;YAGD,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAChF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,MAAM,CAAC,GAAyB,EAAE,GAAa;QACnD,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAGxE,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBACrE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,CAAC,CAAC;YACtF,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;gBAClD,KAAK,EAAE;oBACL,cAAc,EAAE;wBACd,MAAM;wBACN,OAAO;qBACR;iBACF;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,SAAS,CAAC,cAAc,GAAG,QAAQ,EAAE,CAAC;gBACxC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,KAAK,EAAE,sCAAsC,SAAS,CAAC,cAAc,gBAAgB,QAAQ,EAAE;iBAChG,CAAC,CAAC;YACL,CAAC;YAGD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChD,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;YACpE,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBAEpD,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;oBACjD,IAAI,EAAE;wBACJ,MAAM;wBACN,OAAO;wBACP,UAAU;wBACV,QAAQ;wBACR,WAAW;wBACX,MAAM,EAAE,UAAU;qBACnB;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;yBAC7C;wBACD,KAAK,EAAE;4BACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;yBAC7D;wBACD,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;yBAC9C;qBACF;iBACF,CAAC,CAAC;gBAGH,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;oBACxB,KAAK,EAAE;wBACL,cAAc,EAAE;4BACd,MAAM;4BACN,OAAO;yBACR;qBACF;oBACD,IAAI,EAAE;wBACJ,cAAc,EAAE;4BACd,SAAS,EAAE,QAAQ;yBACpB;qBACF;iBACF,CAAC,CAAC;gBAGH,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACvB,IAAI,EAAE;wBACJ,MAAM,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE;wBACpB,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,YAAY;wBACtB,UAAU,EAAE,UAAU,CAAC,EAAE;wBACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;4BACxB,MAAM;4BACN,OAAO;4BACP,UAAU;4BACV,QAAQ;4BACR,WAAW;yBACZ,CAAC;qBACH;iBACF,CAAC,CAAC;gBAEH,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,GAAyB,EAAE,GAAa;QACxD,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAC1B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAE5C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,OAAO,EAAE;oBACP,IAAI,EAAE,IAAI;oBACV,KAAK,EAAE,IAAI;iBACZ;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;gBACrC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC,CAAC;YAC5E,CAAC;YAGD,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAChF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAC1D,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;gBAEpD,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC,eAAe,CAAC,MAAM,CAAC;oBACxD,KAAK,EAAE,EAAE,EAAE,EAAE;oBACb,IAAI,EAAE;wBACJ,MAAM,EAAE,UAAU;wBAClB,UAAU,EAAE,IAAI,IAAI,EAAE;wBACtB,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;4BACnC,GAAG,UAAU,CAAC,WAAW,iBAAiB,KAAK,IAAI,UAAU,EAAE,CAAC,CAAC;4BACjE,aAAa,KAAK,IAAI,UAAU,EAAE;qBACrC;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;yBAC7C;wBACD,KAAK,EAAE;4BACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;yBAC7D;wBACD,IAAI,EAAE;4BACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;yBAC9C;qBACF;iBACF,CAAC,CAAC;gBAGH,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;oBACxB,KAAK,EAAE;wBACL,cAAc,EAAE;4BACd,MAAM,EAAE,UAAU,CAAC,MAAM;4BACzB,OAAO,EAAE,UAAU,CAAC,OAAO;yBAC5B;qBACF;oBACD,IAAI,EAAE;wBACJ,cAAc,EAAE;4BACd,SAAS,EAAE,UAAU,CAAC,QAAQ;yBAC/B;qBACF;iBACF,CAAC,CAAC;gBAGH,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACvB,IAAI,EAAE;wBACJ,MAAM,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE;wBACpB,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,YAAY;wBACtB,UAAU,EAAE,UAAU,CAAC,EAAE;wBACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;wBACjD,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;4BACxB,MAAM,EAAE,UAAU;4BAClB,eAAe;4BACf,KAAK;yBACN,CAAC;qBACH;iBACF,CAAC,CAAC;gBAEH,OAAO,iBAAiB,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,MAAM,CAAC,GAAyB,EAAE,GAAa;QACnD,IAAI,CAAC;YACH,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;YAC1B,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAEzC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBACzD,KAAK,EAAE,EAAE,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;YACjE,CAAC;YAGD,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK,gBAAQ,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAChF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;gBAC5D,KAAK,EAAE,EAAE,EAAE,EAAE;gBACb,IAAI,EAAE;oBACJ,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,CAAC;oBACzB,GAAG,CAAC,WAAW,IAAI,EAAE,WAAW,EAAE,CAAC;iBACpC;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE;wBACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;qBAC7C;oBACD,KAAK,EAAE;wBACL,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;qBAC7D;oBACD,IAAI,EAAE;wBACJ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;qBAC9C;iBACF;aACF,CAAC,CAAC;YAGH,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,IAAI,EAAE;oBACJ,MAAM,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE;oBACpB,MAAM,EAAE,QAAQ;oBAChB,QAAQ,EAAE,YAAY;oBACtB,UAAU,EAAE,EAAE;oBACd,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;wBACxB,MAAM,EAAE,UAAU,CAAC,MAAM;wBACzB,WAAW,EAAE,UAAU,CAAC,WAAW;qBACpC,CAAC;oBACF,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;iBACnD;aACF,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;CACF,CAAC"}