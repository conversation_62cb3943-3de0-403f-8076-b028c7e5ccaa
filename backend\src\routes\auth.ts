import { Router } from 'express';
import { authController } from '../controllers/authController';
import { validate } from '../utils/validation';
import { authSchemas } from '../utils/validation';
import { authenticate } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// Public routes
router.post('/login', validate(authSchemas.login), asyncHandler(authController.login));
router.post('/register', validate(authSchemas.register), asyncHandler(authController.register));
router.post('/refresh', asyncHandler(authController.refreshToken));

// Protected routes
router.post('/logout', authenticate, asyncHandler(authController.logout));
router.get('/me', authenticate, asyncHandler(authController.getCurrentUser));
router.put('/profile', authenticate, asyncHandler(authController.updateProfile));
router.put('/password', authenticate, asyncHandler(authController.changePassword));

export default router;
