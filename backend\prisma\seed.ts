import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create bases
  const base1 = await prisma.base.create({
    data: {
      name: 'Fort Liberty',
      code: 'FL001',
      location: 'North Carolina, USA',
      isActive: true,
    },
  });

  await prisma.base.create({
    data: {
      name: 'Camp Pendleton',
      code: 'CP002',
      location: 'California, USA',
      isActive: true,
    },
  });

  console.log('✅ Created bases');

  // Create users
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'admin',
      firstName: 'System',
      lastName: 'Administrator',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
      isActive: true,
    },
  });

  const commanderUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'commander1',
      firstName: '<PERSON>',
      lastName: 'Commander',
      password: await bcrypt.hash('commander123', 10),
      role: 'BASE_COMMANDER',
      baseId: base1.id,
      isActive: true,
    },
  });

  const logisticsUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'logistics1',
      firstName: 'Jane',
      lastName: 'Logistics',
      password: await bcrypt.hash('logistics123', 10),
      role: 'LOGISTICS_OFFICER',
      baseId: base1.id,
      isActive: true,
    },
  });

  // Update base commander
  await prisma.base.update({
    where: { id: base1.id },
    data: { commanderId: commanderUser.id },
  });

  console.log('✅ Created users');

  // Create assets
  const assets = [
    {
      name: 'M4A1 Carbine',
      code: 'WPN-M4A1',
      category: 'WEAPONS',
      description: 'Standard issue assault rifle',
      unitPrice: 1200.00,
    },
    {
      name: 'M1151 HMMWV',
      code: 'VEH-HMMWV',
      category: 'VEHICLES',
      description: 'High Mobility Multipurpose Wheeled Vehicle',
      unitPrice: 220000.00,
    },
    {
      name: '5.56mm Ammunition',
      code: 'AMM-556',
      category: 'AMMUNITION',
      description: '5.56×45mm NATO ammunition',
      unitPrice: 0.75,
    },
    {
      name: 'AN/PRC-152 Radio',
      code: 'COM-PRC152',
      category: 'COMMUNICATION',
      description: 'Multiband handheld radio',
      unitPrice: 8500.00,
    },
    {
      name: 'First Aid Kit',
      code: 'MED-FAK',
      category: 'MEDICAL',
      description: 'Individual first aid kit',
      unitPrice: 45.00,
    },
    {
      name: 'MRE',
      code: 'SUP-MRE',
      category: 'SUPPLIES',
      description: 'Meal Ready to Eat',
      unitPrice: 8.50,
    },
  ];

  const createdAssets = [];
  for (const asset of assets) {
    const createdAsset = await prisma.asset.create({
      data: asset,
    });
    createdAssets.push(createdAsset);
  }

  console.log('✅ Created assets');

  // Create initial inventory for base1
  const inventoryData = [
    { assetId: createdAssets[0].id, openingBalance: 100, currentBalance: 85, assignedCount: 15, availableCount: 85 }, // M4A1
    { assetId: createdAssets[1].id, openingBalance: 20, currentBalance: 18, assignedCount: 2, availableCount: 18 }, // HMMWV
    { assetId: createdAssets[2].id, openingBalance: 10000, currentBalance: 8500, assignedCount: 0, availableCount: 8500 }, // Ammo
    { assetId: createdAssets[3].id, openingBalance: 50, currentBalance: 45, assignedCount: 5, availableCount: 45 }, // Radio
    { assetId: createdAssets[4].id, openingBalance: 200, currentBalance: 180, assignedCount: 20, availableCount: 180 }, // First Aid
    { assetId: createdAssets[5].id, openingBalance: 1000, currentBalance: 750, assignedCount: 0, availableCount: 750 }, // MRE
  ];

  for (const inventory of inventoryData) {
    await prisma.inventory.create({
      data: {
        baseId: base1.id,
        ...inventory,
      },
    });
  }

  console.log('✅ Created inventory');

  // Create sample transactions
  const sampleTransactions = [
    {
      type: 'PURCHASE',
      baseId: base1.id,
      assetId: createdAssets[0].id,
      quantity: 25,
      unitPrice: 1200.00,
      totalValue: 30000.00,
      description: 'Quarterly weapon procurement',
      createdById: logisticsUser.id,
      approvedById: commanderUser.id,
      approvedAt: new Date(),
    },
    {
      type: 'ASSIGNMENT',
      baseId: base1.id,
      assetId: createdAssets[0].id,
      quantity: 15,
      unitPrice: 1200.00,
      totalValue: 18000.00,
      description: 'Weapons assigned to Alpha Company',
      createdById: logisticsUser.id,
      approvedById: commanderUser.id,
      approvedAt: new Date(),
    },
    {
      type: 'EXPENDITURE',
      baseId: base1.id,
      assetId: createdAssets[2].id,
      quantity: 1500,
      unitPrice: 0.75,
      totalValue: 1125.00,
      description: 'Training exercise ammunition consumption',
      createdById: logisticsUser.id,
      approvedById: commanderUser.id,
      approvedAt: new Date(),
    },
  ];

  for (const transaction of sampleTransactions) {
    await prisma.transaction.create({
      data: transaction,
    });
  }

  console.log('✅ Created sample transactions');

  // Create a sample purchase
  const purchase = await prisma.purchase.create({
    data: {
      baseId: base1.id,
      vendor: 'Defense Contractor Inc.',
      totalAmount: 50000.00,
      description: 'Monthly equipment procurement',
      createdById: logisticsUser.id,
    },
  });

  await prisma.purchaseItem.create({
    data: {
      purchaseId: purchase.id,
      assetId: createdAssets[3].id,
      quantity: 5,
      unitPrice: 8500.00,
      totalPrice: 42500.00,
    },
  });

  await prisma.purchaseItem.create({
    data: {
      purchaseId: purchase.id,
      assetId: createdAssets[4].id,
      quantity: 150,
      unitPrice: 45.00,
      totalPrice: 6750.00,
    },
  });

  console.log('✅ Created sample purchase');

  // Create sample asset assignments
  await prisma.assetAssignment.create({
    data: {
      baseId: base1.id,
      assetId: createdAssets[0].id,
      assignedTo: commanderUser.id,
      quantity: 1,
      status: 'ASSIGNED',
      description: 'Command weapon assignment',
    },
  });

  await prisma.assetAssignment.create({
    data: {
      baseId: base1.id,
      assetId: createdAssets[1].id,
      assignedTo: commanderUser.id,
      quantity: 1,
      status: 'ASSIGNED',
      description: 'Command vehicle assignment',
    },
  });

  console.log('✅ Created sample assignments');

  console.log('🎉 Database seed completed successfully!');
  console.log('\n📋 Demo Login Credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Base Commander: <EMAIL> / commander123');
  console.log('Logistics Officer: <EMAIL> / logistics123');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
