import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { transfersAPI, basesAPI, assetsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const Transfers = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    baseId: '',
    startDate: '',
    endDate: '',
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
  });

  // Fetch transfers
  const { data: transfersData, isLoading: transfersLoading } = useQuery({
    queryKey: ['transfers', filters, pagination],
    queryFn: () => transfersAPI.getAll({ ...filters, ...pagination }),
  });

  // Fetch bases for filters and forms
  const { data: basesData } = useQuery({
    queryKey: ['bases'],
    queryFn: () => basesAPI.getAll(),
  });

  // Fetch assets for transfer creation
  const { data: assetsData } = useQuery({
    queryKey: ['assets'],
    queryFn: () => assetsAPI.getAll(),
  });

  const transfers = transfersData?.transfers || [];
  const totalPages = transfersData?.pagination?.pages || 1;
  const bases = basesData?.bases || [];
  const assets = assetsData?.assets || [];

  // Status badge component
  const StatusBadge = ({ status }) => {
    const statusColors = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      APPROVED: 'bg-blue-100 text-blue-800',
      IN_TRANSIT: 'bg-purple-100 text-purple-800',
      COMPLETED: 'bg-green-100 text-green-800',
      CANCELLED: 'bg-red-100 text-red-800',
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // View transfer details
  const handleViewTransfer = (transfer) => {
    setSelectedTransfer(transfer);
    setShowDetailModal(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Transfer Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage inter-base asset transfers and approvals.
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Create Transfer
        </button>
      </div>

      {/* Filters */}
      <div className="card p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="PENDING">Pending</option>
              <option value="APPROVED">Approved</option>
              <option value="IN_TRANSIT">In Transit</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Base</label>
            <select
              value={filters.baseId}
              onChange={(e) => handleFilterChange('baseId', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Bases</option>
              {bases.map(base => (
                <option key={base.id} value={base.id}>{base.name}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              type="date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Transfers Table */}
      <div className="card overflow-hidden">
        {transfersLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading transfers...</p>
          </div>
        ) : transfers.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No transfers found matching your criteria.
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transfer ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      From Base
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      To Base
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Items
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Requested Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transfers.map((transfer) => (
                    <tr key={transfer.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {transfer.id.slice(-8)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transfer.fromBase?.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transfer.toBase?.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={transfer.status} />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {transfer.items?.length || 0} items
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(transfer.requestedAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleViewTransfer(transfer)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing page <span className="font-medium">{pagination.page}</span> of{' '}
                      <span className="font-medium">{totalPages}</span>
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Create Transfer Modal */}
      {showCreateModal && (
        <CreateTransferModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          bases={bases}
          assets={assets}
          user={user}
          onSuccess={() => {
            queryClient.invalidateQueries(['transfers']);
            setShowCreateModal(false);
          }}
        />
      )}

      {/* Transfer Detail Modal */}
      {showDetailModal && selectedTransfer && (
        <TransferDetailModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedTransfer(null);
          }}
          transfer={selectedTransfer}
          user={user}
          onSuccess={() => {
            queryClient.invalidateQueries(['transfers']);
            setShowDetailModal(false);
            setSelectedTransfer(null);
          }}
        />
      )}
    </div>
  );
};

// Create Transfer Modal Component
const CreateTransferModal = ({ isOpen, onClose, bases, assets, user, onSuccess }) => {
  const [formData, setFormData] = useState({
    fromBaseId: user?.baseId || '',
    toBaseId: '',
    description: '',
    items: [{ assetId: '', quantity: 1 }],
  });
  const [errors, setErrors] = useState({});

  const createTransferMutation = useMutation({
    mutationFn: transfersAPI.create,
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      setErrors({ submit: error.response?.data?.error || 'Failed to create transfer' });
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors = {};
    if (!formData.fromBaseId) newErrors.fromBaseId = 'From base is required';
    if (!formData.toBaseId) newErrors.toBaseId = 'To base is required';
    if (formData.fromBaseId === formData.toBaseId) newErrors.toBaseId = 'Cannot transfer to the same base';
    if (formData.items.some(item => !item.assetId || item.quantity <= 0)) {
      newErrors.items = 'All items must have valid asset and quantity';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    createTransferMutation.mutate(formData);
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { assetId: '', quantity: 1 }],
    }));
  };

  const removeItem = (index) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const updateItem = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Create Transfer Request</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">From Base</label>
              <select
                value={formData.fromBaseId}
                onChange={(e) => setFormData(prev => ({ ...prev, fromBaseId: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={user?.role !== 'ADMIN'}
              >
                <option value="">Select base</option>
                {bases.map(base => (
                  <option key={base.id} value={base.id}>{base.name}</option>
                ))}
              </select>
              {errors.fromBaseId && <p className="text-red-500 text-sm mt-1">{errors.fromBaseId}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">To Base</label>
              <select
                value={formData.toBaseId}
                onChange={(e) => setFormData(prev => ({ ...prev, toBaseId: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select base</option>
                {bases.filter(base => base.id !== formData.fromBaseId).map(base => (
                  <option key={base.id} value={base.id}>{base.name}</option>
                ))}
              </select>
              {errors.toBaseId && <p className="text-red-500 text-sm mt-1">{errors.toBaseId}</p>}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Optional description for the transfer"
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Items to Transfer</label>
              <button
                type="button"
                onClick={addItem}
                className="text-blue-600 hover:text-blue-800 text-sm"
              >
                + Add Item
              </button>
            </div>

            {formData.items.map((item, index) => (
              <div key={index} className="flex gap-2 mb-2">
                <select
                  value={item.assetId}
                  onChange={(e) => updateItem(index, 'assetId', e.target.value)}
                  className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select asset</option>
                  {assets.map(asset => (
                    <option key={asset.id} value={asset.id}>{asset.name} ({asset.code})</option>
                  ))}
                </select>
                <input
                  type="number"
                  min="1"
                  value={item.quantity}
                  onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                  className="w-20 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {formData.items.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeItem(index)}
                    className="text-red-600 hover:text-red-800 px-2"
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            {errors.items && <p className="text-red-500 text-sm mt-1">{errors.items}</p>}
          </div>

          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{errors.submit}</p>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={createTransferMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {createTransferMutation.isPending ? 'Creating...' : 'Create Transfer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Transfer Detail Modal Component
const TransferDetailModal = ({ isOpen, onClose, transfer, user, onSuccess }) => {
  const [showApprovalForm, setShowApprovalForm] = useState(false);

  const approveTransferMutation = useMutation({
    mutationFn: (id) => transfersAPI.approve(id),
    onSuccess: () => {
      onSuccess();
    },
  });

  const completeTransferMutation = useMutation({
    mutationFn: (id) => transfersAPI.complete(id),
    onSuccess: () => {
      onSuccess();
    },
  });

  const cancelTransferMutation = useMutation({
    mutationFn: (id) => transfersAPI.cancel(id),
    onSuccess: () => {
      onSuccess();
    },
  });

  const canApprove = user?.role === 'ADMIN' || (user?.role === 'BASE_COMMANDER' && user?.baseId === transfer.toBaseId);
  const canComplete = transfer.status === 'IN_TRANSIT' && (user?.role === 'ADMIN' || user?.baseId === transfer.toBaseId);
  const canCancel = transfer.status === 'PENDING' && (user?.role === 'ADMIN' || user?.baseId === transfer.fromBaseId);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Transfer Details</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-3">Transfer Information</h3>
            <div className="space-y-2">
              <div>
                <span className="font-medium">Transfer ID:</span> {transfer.id.slice(-8)}
              </div>
              <div>
                <span className="font-medium">Status:</span>
                <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${
                  transfer.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                  transfer.status === 'APPROVED' ? 'bg-blue-100 text-blue-800' :
                  transfer.status === 'IN_TRANSIT' ? 'bg-purple-100 text-purple-800' :
                  transfer.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {transfer.status}
                </span>
              </div>
              <div>
                <span className="font-medium">From Base:</span> {transfer.fromBase?.name}
              </div>
              <div>
                <span className="font-medium">To Base:</span> {transfer.toBase?.name}
              </div>
              <div>
                <span className="font-medium">Requested Date:</span> {new Date(transfer.requestedAt).toLocaleString()}
              </div>
              {transfer.approvedAt && (
                <div>
                  <span className="font-medium">Approved Date:</span> {new Date(transfer.approvedAt).toLocaleString()}
                </div>
              )}
              {transfer.completedAt && (
                <div>
                  <span className="font-medium">Completed Date:</span> {new Date(transfer.completedAt).toLocaleString()}
                </div>
              )}
              {transfer.description && (
                <div>
                  <span className="font-medium">Description:</span> {transfer.description}
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-3">Transfer Items</h3>
            <div className="space-y-2">
              {transfer.items?.map((item, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">{item.asset?.name}</div>
                    <div className="text-sm text-gray-600">{item.asset?.code}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">Qty: {item.quantity}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          {canApprove && transfer.status === 'PENDING' && (
            <button
              onClick={() => approveTransferMutation.mutate(transfer.id)}
              disabled={approveTransferMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {approveTransferMutation.isPending ? 'Approving...' : 'Approve Transfer'}
            </button>
          )}

          {canComplete && (
            <button
              onClick={() => completeTransferMutation.mutate(transfer.id)}
              disabled={completeTransferMutation.isPending}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {completeTransferMutation.isPending ? 'Completing...' : 'Mark Complete'}
            </button>
          )}

          {canCancel && (
            <button
              onClick={() => cancelTransferMutation.mutate(transfer.id)}
              disabled={cancelTransferMutation.isPending}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {cancelTransferMutation.isPending ? 'Cancelling...' : 'Cancel Transfer'}
            </button>
          )}

          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default Transfers;
