import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { CreatePurchaseRequest } from '../types/api';
import { TransactionType } from '../types/enums';
import { AuthenticatedRequest } from '../middleware/auth';

const prisma = new PrismaClient();

export const purchaseController = {
  // Get all purchases
  async getAllPurchases(req: Request, res: Response) {
    try {
      const { page = 1, limit = 10, baseId, startDate, endDate } = req.query;
      const skip = (Number(page) - 1) * Number(limit);

      // Build where clause
      const where: any = {};
      
      // Base filter (from auth middleware)
      if (baseId) {
        where.baseId = baseId as string;
      }

      // Date range filter
      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = new Date(startDate as string);
        if (endDate) where.createdAt.lte = new Date(endDate as string);
      }

      const [purchases, total] = await Promise.all([
        prisma.purchase.findMany({
          where,
          skip,
          take: Number(limit),
          include: {
            base: {
              select: {
                id: true,
                name: true,
                code: true,
                location: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            items: {
              include: {
                asset: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                    category: true,
                    unitPrice: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        prisma.purchase.count({ where }),
      ]);

      res.json({
        purchases,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      });
    } catch (error) {
      console.error('Error fetching purchases:', error);
      res.status(500).json({ error: 'Failed to fetch purchases' });
    }
  },

  // Get purchase by ID
  async getPurchaseById(req: Request, res: Response): Promise<Response | void> {
    try {
      const { id } = req.params;

      const purchase = await prisma.purchase.findUnique({
        where: { id },
        include: {
          base: {
            select: {
              id: true,
              name: true,
              code: true,
              location: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          items: {
            include: {
              asset: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                  category: true,
                  unitPrice: true,
                },
              },
            },
          },
        },
      });

      if (!purchase) {
        return res.status(404).json({ error: 'Purchase not found' });
      }

      res.json({ purchase });
    } catch (error) {
      console.error('Error fetching purchase:', error);
      res.status(500).json({ error: 'Failed to fetch purchase' });
    }
  },

  // Create new purchase
  async createPurchase(req: AuthenticatedRequest, res: Response): Promise<Response | void> {
    try {
      const { baseId, vendor, description, items }: CreatePurchaseRequest = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Calculate total amount
      const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

      // Start transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create purchase record
        const purchase = await tx.purchase.create({
          data: {
            baseId,
            vendor,
            description: description || null,
            totalAmount,
            createdById: userId,
          },
        });

        // Create purchase items
        await Promise.all(
          items.map(item =>
            tx.purchaseItem.create({
              data: {
                purchaseId: purchase.id,
                assetId: item.assetId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.quantity * item.unitPrice,
              },
            })
          )
        );

        // Update inventory for each item
        for (const item of items) {
          await tx.inventory.upsert({
            where: {
              baseId_assetId: {
                baseId,
                assetId: item.assetId,
              },
            },
            update: {
              currentBalance: {
                increment: item.quantity,
              },
              availableCount: {
                increment: item.quantity,
              },
            },
            create: {
              baseId,
              assetId: item.assetId,
              openingBalance: item.quantity,
              currentBalance: item.quantity,
              assignedCount: 0,
              availableCount: item.quantity,
            },
          });

          // Create transaction record
          await tx.transaction.create({
            data: {
              type: TransactionType.PURCHASE,
              assetId: item.assetId,
              baseId,
              quantity: item.quantity,
              totalValue: item.quantity * item.unitPrice,
              referenceId: purchase.id,
              description: `Purchase from ${vendor}`,
              createdById: userId,
            },
          });
        }

        return purchase;
      });

      // Fetch the complete purchase with relations
      const completePurchase = await prisma.purchase.findUnique({
        where: { id: result.id },
        include: {
          base: {
            select: {
              id: true,
              name: true,
              code: true,
              location: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          items: {
            include: {
              asset: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                  category: true,
                  unitPrice: true,
                },
              },
            },
          },
        },
      });

      res.status(201).json({ purchase: completePurchase });
    } catch (error) {
      console.error('Error creating purchase:', error);
      res.status(500).json({ error: 'Failed to create purchase' });
    }
  },

  // Update purchase
  async updatePurchase(req: Request, res: Response): Promise<Response | void> {
    try {
      const { id } = req.params;
      const { vendor, description, items }: CreatePurchaseRequest = req.body;

      // Check if purchase exists
      const existingPurchase = await prisma.purchase.findUnique({
        where: { id },
        include: { items: true },
      });

      if (!existingPurchase) {
        return res.status(404).json({ error: 'Purchase not found' });
      }

      // Calculate new total amount
      const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

      // Start transaction
      await prisma.$transaction(async (tx) => {
        // Update purchase record
        const purchase = await tx.purchase.update({
          where: { id },
          data: {
            vendor,
            description: description || null,
            totalAmount,
          },
        });

        // Delete existing items
        await tx.purchaseItem.deleteMany({
          where: { purchaseId: id },
        });

        // Create new purchase items
        await Promise.all(
          items.map(item =>
            tx.purchaseItem.create({
              data: {
                purchaseId: id,
                assetId: item.assetId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.quantity * item.unitPrice,
              },
            })
          )
        );

        return purchase;
      });

      // Fetch the complete updated purchase
      const updatedPurchase = await prisma.purchase.findUnique({
        where: { id },
        include: {
          base: {
            select: {
              id: true,
              name: true,
              code: true,
              location: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          items: {
            include: {
              asset: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                  category: true,
                  unitPrice: true,
                },
              },
            },
          },
        },
      });

      res.json({ purchase: updatedPurchase });
    } catch (error) {
      console.error('Error updating purchase:', error);
      res.status(500).json({ error: 'Failed to update purchase' });
    }
  },

  // Delete purchase
  async deletePurchase(req: Request, res: Response): Promise<Response | void> {
    try {
      const { id } = req.params;

      // Check if purchase exists
      const existingPurchase = await prisma.purchase.findUnique({
        where: { id },
        include: { items: true },
      });

      if (!existingPurchase) {
        return res.status(404).json({ error: 'Purchase not found' });
      }

      // Start transaction to delete purchase and revert inventory
      await prisma.$transaction(async (tx) => {
        // Revert inventory changes
        for (const item of existingPurchase.items) {
          await tx.inventory.update({
            where: {
              baseId_assetId: {
                baseId: existingPurchase.baseId,
                assetId: item.assetId,
              },
            },
            data: {
              currentBalance: {
                decrement: item.quantity,
              },
              availableCount: {
                decrement: item.quantity,
              },
            },
          });
        }

        // Delete related transactions
        await tx.transaction.deleteMany({
          where: {
            referenceId: id,
            type: TransactionType.PURCHASE,
          },
        });

        // Delete purchase items
        await tx.purchaseItem.deleteMany({
          where: { purchaseId: id },
        });

        // Delete purchase
        await tx.purchase.delete({
          where: { id },
        });
      });

      res.json({ message: 'Purchase deleted successfully' });
    } catch (error) {
      console.error('Error deleting purchase:', error);
      res.status(500).json({ error: 'Failed to delete purchase' });
    }
  },
};
