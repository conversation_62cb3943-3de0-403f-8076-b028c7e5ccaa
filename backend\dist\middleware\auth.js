"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeResourceOwnership = exports.authorizeBase = exports.authorize = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Access token required' });
            return;
        }
        const token = authHeader.substring(7);
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            logger_1.logger.error('JWT_SECRET not configured');
            res.status(500).json({ error: 'Server configuration error' });
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        const user = await prisma.user.findUnique({
            where: { id: decoded.userId },
            select: {
                id: true,
                email: true,
                username: true,
                role: true,
                baseId: true,
                isActive: true,
            },
        });
        if (!user || !user.isActive) {
            res.status(401).json({ error: 'Invalid or inactive user' });
            return;
        }
        await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() },
        });
        req.user = {
            ...user,
            role: user.role
        };
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            res.status(401).json({ error: 'Invalid token' });
            return;
        }
        logger_1.logger.error('Authentication error:', error);
        res.status(500).json({ error: 'Authentication failed' });
    }
};
exports.authenticate = authenticate;
const authorize = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        if (!allowedRoles.includes(req.user.role)) {
            res.status(403).json({
                error: 'Insufficient permissions',
                required: allowedRoles,
                current: req.user.role,
            });
            return;
        }
        next();
    };
};
exports.authorize = authorize;
const authorizeBase = (req, res, next) => {
    if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
    }
    if (req.user.role === enums_1.UserRole.ADMIN) {
        next();
        return;
    }
    const requestedBaseId = req.params.baseId || req.query.baseId || req.body.baseId;
    if (!requestedBaseId) {
        res.status(400).json({ error: 'Base ID required' });
        return;
    }
    if (req.user.baseId !== requestedBaseId) {
        res.status(403).json({
            error: 'Access denied to this base',
            userBase: req.user.baseId,
            requestedBase: requestedBaseId,
        });
        return;
    }
    next();
};
exports.authorizeBase = authorizeBase;
const authorizeResourceOwnership = (resourceType) => {
    return async (req, res, next) => {
        if (!req.user) {
            res.status(401).json({ error: 'Authentication required' });
            return;
        }
        if (req.user.role === enums_1.UserRole.ADMIN) {
            next();
            return;
        }
        try {
            const resourceId = req.params.id;
            if (!resourceId) {
                res.status(400).json({ error: 'Resource ID required' });
                return;
            }
            let resource;
            switch (resourceType) {
                case 'purchase':
                    resource = await prisma.purchase.findUnique({
                        where: { id: resourceId },
                        select: { baseId: true },
                    });
                    break;
                case 'transfer':
                    resource = await prisma.transfer.findUnique({
                        where: { id: resourceId },
                        select: { fromBaseId: true, toBaseId: true },
                    });
                    break;
                case 'assignment':
                    resource = await prisma.assetAssignment.findUnique({
                        where: { id: resourceId },
                        select: { baseId: true },
                    });
                    break;
                default:
                    res.status(400).json({ error: 'Invalid resource type' });
                    return;
            }
            if (!resource) {
                res.status(404).json({ error: 'Resource not found' });
                return;
            }
            const hasAccess = resourceType === 'transfer'
                ? resource.fromBaseId === req.user.baseId || resource.toBaseId === req.user.baseId
                : resource.baseId === req.user.baseId;
            if (!hasAccess) {
                res.status(403).json({ error: 'Access denied to this resource' });
                return;
            }
            next();
        }
        catch (error) {
            logger_1.logger.error(`Authorization error for ${resourceType}:`, error);
            res.status(500).json({ error: 'Authorization failed' });
        }
    };
};
exports.authorizeResourceOwnership = authorizeResourceOwnership;
//# sourceMappingURL=auth.js.map