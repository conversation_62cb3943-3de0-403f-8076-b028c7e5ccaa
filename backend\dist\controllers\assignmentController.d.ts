import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        baseId: string;
    };
}
export declare const assignmentController: {
    getAll(req: AuthenticatedRequest, res: Response): Promise<void>;
    getById(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    create(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    returnAsset(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
    update(req: AuthenticatedRequest, res: Response): Promise<Response | void>;
};
export {};
//# sourceMappingURL=assignmentController.d.ts.map