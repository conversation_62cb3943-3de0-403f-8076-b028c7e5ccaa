import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        baseId: string;
    };
}
export declare const assignmentController: {
    getAll(req: AuthenticatedRequest, res: Response): Promise<void>;
    getById(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    create(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    returnAsset(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    update(req: AuthenticatedRequest, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
};
export {};
//# sourceMappingURL=assignmentController.d.ts.map