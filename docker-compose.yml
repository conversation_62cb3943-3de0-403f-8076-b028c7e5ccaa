version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: military_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: military_assets
      POSTGRES_USER: military_user
      POSTGRES_PASSWORD: military_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - military_network

  # Redis for session management (optional)
  redis:
    image: redis:7-alpine
    container_name: military_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - military_network

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: military_backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: **********************************************************/military_assets
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - military_network
    command: npm run dev

  # Frontend React App (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: military_frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:3001/api
      VITE_APP_NAME: Military Asset Management System
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - military_network
    command: npm run dev

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: military_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - military_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  military_network:
    driver: bridge
