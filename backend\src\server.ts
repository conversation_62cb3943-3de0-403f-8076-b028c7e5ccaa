console.log('Loading server dependencies...');
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
// import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
console.log('Dependencies loaded successfully');

// Temporarily comment out problematic imports for debugging
// import { errorHandler } from './middleware/errorHandler';
// import { notFoundHandler } from './middleware/notFoundHandler';
// import { auditLogger } from './middleware/auditLogger';
// import { logger } from './utils/logger';

// Import routes
console.log('Loading routes...');
// import authRoutes from './routes/auth';
// import dashboardRoutes from './routes/dashboard';
// import assetsRoutes from './routes/assets';
// import inventoryRoutes from './routes/inventory';
// import purchasesRoutes from './routes/purchases';
// import transfersRoutes from './routes/transfers';
// import assignmentsRoutes from './routes/assignments';
// import usersRoutes from './routes/users';
// import basesRoutes from './routes/bases';
// import auditRoutes from './routes/audit';
console.log('Routes loaded (commented out for debugging)');

// Load environment variables
console.log('Loading environment variables...');
dotenv.config();
console.log('Environment variables loaded');

console.log('Initializing Express app...');
const app = express();
const PORT = process.env.PORT || 3001;
console.log(`Port configured: ${PORT}`);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? process.env.FRONTEND_URL 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware (commented out for debugging)
// app.use(morgan('combined', {
//   stream: {
//     write: (message: string) => {
//       logger.info(message.trim());
//     },
//   },
// }));

// Audit logging middleware (commented out for debugging)
// app.use(auditLogger);

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// API routes (commented out for debugging)
// app.use('/api/auth', authRoutes);
// app.use('/api/dashboard', dashboardRoutes);
// app.use('/api/assets', assetsRoutes);
// app.use('/api/inventory', inventoryRoutes);
// app.use('/api/purchases', purchasesRoutes);
// app.use('/api/transfers', transfersRoutes);
// app.use('/api/assignments', assignmentsRoutes);
// app.use('/api/users', usersRoutes);
// app.use('/api/bases', basesRoutes);
// app.use('/api/audit', auditRoutes);

// API documentation endpoint
app.get('/api', (_req, res) => {
  res.json({
    name: 'Military Asset Management System API',
    version: '1.0.0',
    description: 'RESTful API for managing military assets across multiple bases',
    endpoints: {
      auth: '/api/auth',
      dashboard: '/api/dashboard',
      assets: '/api/assets',
      inventory: '/api/inventory',
      purchases: '/api/purchases',
      transfers: '/api/transfers',
      assignments: '/api/assignments',
      users: '/api/users',
      bases: '/api/bases',
      audit: '/api/audit',
    },
    documentation: '/api/docs',
  });
});

// Error handling middleware (must be last) - commented out for debugging
// app.use(notFoundHandler);
// app.use(errorHandler);

// Graceful shutdown (commented out for debugging)
// process.on('SIGTERM', () => {
//   logger.info('SIGTERM received, shutting down gracefully');
//   process.exit(0);
// });

// process.on('SIGINT', () => {
//   logger.info('SIGINT received, shutting down gracefully');
//   process.exit(0);
// });

// Start server
console.log('Starting server...');
app.listen(PORT, () => {
  console.log(`🚀 Military Asset Management API server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API docs: http://localhost:${PORT}/api`);
});

export default app;
