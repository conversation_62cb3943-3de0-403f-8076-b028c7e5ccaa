import { Router } from 'express';
import { dashboardController } from '../controllers/dashboardController';
import { authenticate, authorizeBase } from '../middleware/auth';
import { validateQuery } from '../utils/validation';
import { querySchemas } from '../utils/validation';
import { asyncHandler } from '../middleware/errorHandler';


const router = Router();

// All dashboard routes require authentication
router.use(authenticate);

// Get dashboard overview
router.get(
  '/overview',
  validateQuery(querySchemas.baseFilter.concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(dashboardController.getOverview)
);

// Get key metrics
router.get(
  '/metrics',
  validateQuery(querySchemas.baseFilter.concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(dashboardController.getMetrics)
);

// Get net movement details (for popup)
router.get(
  '/net-movement/:baseId',
  validateQuery(querySchemas.dateRange),
  authorizeBase,
  async<PERSON>andler(dashboardController.getNetMovementDetails)
);

// Get recent activities
router.get(
  '/activities',
  validateQuery(querySchemas.baseFilter.concat(querySchemas.pagination)),
  authorizeBase,
  asyncHandler(dashboardController.getRecentActivities)
);

// Get inventory alerts (low stock, etc.)
router.get(
  '/alerts',
  validateQuery(querySchemas.baseFilter),
  authorizeBase,
  asyncHandler(dashboardController.getInventoryAlerts)
);

// Get dashboard charts data
router.get(
  '/charts',
  validateQuery(querySchemas.baseFilter.concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(dashboardController.getChartsData)
);

export default router;
