# Military Asset Management System - Database Design

## Overview

The database is designed to support comprehensive military asset management across multiple bases with role-based access control, complete audit trails, and real-time inventory tracking.

## Database Choice: PostgreSQL

**Justification:**
- **ACID Compliance**: Critical for military asset tracking where data integrity is paramount
- **Complex Relationships**: Excellent support for foreign keys and complex joins
- **JSON Support**: Flexible storage for audit logs and metadata
- **Performance**: Excellent query optimization for reporting and analytics
- **Security**: Advanced security features including row-level security
- **Scalability**: Handles large datasets efficiently

## Core Entities

### 1. Users
- **Purpose**: Authentication, authorization, and user management
- **Key Fields**: email, username, role, baseId, password (hashed)
- **Relationships**: Belongs to Base, creates/approves Transactions

### 2. Bases
- **Purpose**: Military installation management
- **Key Fields**: name, code, location, commanderId
- **Relationships**: Has many Users, Inventory records, Transfers

### 3. Assets
- **Purpose**: Asset type definitions and specifications
- **Key Fields**: name, code, category, unitPrice
- **Relationships**: Has many Inventory records, Transactions

### 4. Inventory
- **Purpose**: Real-time asset quantities per base
- **Key Fields**: baseId, assetId, openingBalance, currentBalance, assignedCount
- **Relationships**: Belongs to Base and Asset
- **Business Logic**: currentBalance = openingBalance + purchases + transfersIn - transfersOut - assignments - expenditures

### 5. Transactions
- **Purpose**: Complete audit trail of all asset movements
- **Key Fields**: type, baseId, assetId, quantity, createdById, approvedById
- **Relationships**: References Asset, User (creator), User (approver)

## Specialized Entities

### 6. Purchases & PurchaseItems
- **Purpose**: Asset acquisition tracking
- **Structure**: Header-detail pattern for multi-item purchases
- **Key Fields**: vendor, totalAmount, items with quantities and prices

### 7. Transfers & TransferItems
- **Purpose**: Inter-base asset movement
- **Structure**: Header-detail with approval workflow
- **Key Fields**: fromBaseId, toBaseId, status, approval chain

### 8. AssetAssignments
- **Purpose**: Personnel asset assignment tracking
- **Key Fields**: assignedTo, quantity, status, assignedAt, returnedAt

### 9. AuditLog
- **Purpose**: Comprehensive system activity logging
- **Key Fields**: userId, action, resource, oldValues, newValues, timestamp
- **Storage**: JSON fields for flexible audit data

## Key Design Patterns

### 1. Role-Based Access Control (RBAC)
```sql
-- Users have roles that determine access levels
enum UserRole {
  ADMIN           -- Full system access
  BASE_COMMANDER  -- Base-specific operations
  LOGISTICS_OFFICER -- Limited purchase/transfer access
}
```

### 2. Transaction Pattern
- All asset movements recorded in central Transaction table
- Specialized tables (Purchase, Transfer) link via referenceId
- Maintains complete audit trail

### 3. Inventory Management
- Real-time balance calculation
- Separate tracking of assigned vs available quantities
- Opening balance for period-based reporting

### 4. Approval Workflows
- Transfer status tracking (PENDING → APPROVED → IN_TRANSIT → COMPLETED)
- Separate created/approved user tracking
- Timestamp tracking for SLA monitoring

## Data Integrity Constraints

### Primary Keys
- All tables use CUID for distributed-safe unique identifiers
- No auto-incrementing integers to avoid conflicts

### Foreign Key Constraints
- Strict referential integrity between all related entities
- Cascade rules defined for data consistency

### Unique Constraints
- Asset codes, base codes, user emails/usernames
- Composite unique constraints (baseId + assetId for inventory)

### Check Constraints
- Quantity fields must be non-negative
- Balance calculations must be consistent
- Status transitions must follow defined workflows

## Indexing Strategy

### Performance Indexes
```sql
-- Frequently queried combinations
CREATE INDEX idx_inventory_base_asset ON inventory(baseId, assetId);
CREATE INDEX idx_transactions_base_date ON transactions(baseId, createdAt);
CREATE INDEX idx_audit_user_timestamp ON audit_logs(userId, timestamp);
CREATE INDEX idx_transfers_status ON transfers(status);
```

### Reporting Indexes
```sql
-- Dashboard and reporting queries
CREATE INDEX idx_transactions_type_date ON transactions(type, createdAt);
CREATE INDEX idx_assets_category ON assets(category);
CREATE INDEX idx_users_role_base ON users(role, baseId);
```

## Security Considerations

### Data Protection
- Password hashing using bcrypt with high salt rounds
- Sensitive data encryption at application level
- Audit logging for all data access and modifications

### Access Control
- Row-level security policies based on user roles
- Base-specific data isolation for commanders
- API-level authorization middleware

## Scalability Features

### Partitioning Strategy
- Audit logs partitioned by date for performance
- Transaction history archived after retention period
- Read replicas for reporting queries

### Caching Strategy
- Inventory balances cached with invalidation triggers
- User session data in Redis
- Frequently accessed asset definitions cached

## Backup and Recovery

### Data Retention
- Transaction data: Permanent retention
- Audit logs: 7-year retention (configurable)
- User sessions: 24-hour retention

### Backup Strategy
- Daily full backups
- Continuous WAL archiving
- Point-in-time recovery capability
- Cross-region backup replication

## Migration Strategy

### Schema Versioning
- Prisma migrations for schema changes
- Backward compatibility maintained
- Data migration scripts for major changes

### Deployment
- Blue-green deployment for zero downtime
- Database migration validation
- Rollback procedures documented
