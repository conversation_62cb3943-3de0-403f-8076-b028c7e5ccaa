"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignmentController = void 0;
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const prisma = new client_1.PrismaClient();
exports.assignmentController = {
    async getAll(req, res) {
        try {
            const { page = 1, limit = 10, baseId, assetId, status, assignedTo, startDate, endDate, } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);
            let whereClause = {};
            if (req.user?.role === enums_1.UserRole.BASE_COMMANDER || req.user?.role === enums_1.UserRole.LOGISTICS_OFFICER) {
                whereClause.baseId = req.user.baseId;
            }
            if (baseId)
                whereClause.baseId = baseId;
            if (assetId)
                whereClause.assetId = assetId;
            if (status)
                whereClause.status = status;
            if (assignedTo)
                whereClause.assignedTo = assignedTo;
            if (startDate || endDate) {
                whereClause.assignedAt = {};
                if (startDate)
                    whereClause.assignedAt.gte = new Date(startDate);
                if (endDate)
                    whereClause.assignedAt.lte = new Date(endDate);
            }
            const [assignments, total] = await Promise.all([
                prisma.assetAssignment.findMany({
                    where: whereClause,
                    include: {
                        base: {
                            select: { id: true, name: true, code: true }
                        },
                        asset: {
                            select: { id: true, name: true, code: true, category: true }
                        },
                        user: {
                            select: { id: true, name: true, email: true }
                        }
                    },
                    orderBy: { assignedAt: 'desc' },
                    skip,
                    take,
                }),
                prisma.assetAssignment.count({ where: whereClause }),
            ]);
            const totalPages = Math.ceil(total / take);
            res.json({
                assignments,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    pages: totalPages,
                },
            });
        }
        catch (error) {
            console.error('Error fetching assignments:', error);
            res.status(500).json({ error: 'Failed to fetch assignments' });
        }
    },
    async getById(req, res) {
        try {
            const { id } = req.params;
            const assignment = await prisma.assetAssignment.findUnique({
                where: { id },
                include: {
                    base: {
                        select: { id: true, name: true, code: true }
                    },
                    asset: {
                        select: { id: true, name: true, code: true, category: true, description: true }
                    },
                    user: {
                        select: { id: true, name: true, email: true }
                    }
                },
            });
            if (!assignment) {
                return res.status(404).json({ error: 'Assignment not found' });
            }
            if (req.user?.role !== enums_1.UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Access denied' });
            }
            res.json({ assignment });
        }
        catch (error) {
            console.error('Error fetching assignment:', error);
            res.status(500).json({ error: 'Failed to fetch assignment' });
        }
    },
    async create(req, res) {
        try {
            const { baseId, assetId, assignedTo, quantity, description } = req.body;
            if (req.user?.role !== enums_1.UserRole.ADMIN && baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Cannot create assignments for other bases' });
            }
            const inventory = await prisma.inventory.findUnique({
                where: {
                    baseId_assetId: {
                        baseId,
                        assetId,
                    }
                },
                include: {
                    asset: true
                }
            });
            if (!inventory) {
                return res.status(400).json({ error: 'Asset not available at this base' });
            }
            if (inventory.availableCount < quantity) {
                return res.status(400).json({
                    error: `Insufficient inventory. Available: ${inventory.availableCount}, Requested: ${quantity}`
                });
            }
            const assignedUser = await prisma.user.findUnique({
                where: { id: assignedTo }
            });
            if (!assignedUser) {
                return res.status(400).json({ error: 'Assigned user not found' });
            }
            const result = await prisma.$transaction(async (tx) => {
                const assignment = await tx.assetAssignment.create({
                    data: {
                        baseId,
                        assetId,
                        assignedTo,
                        quantity,
                        description,
                        status: 'ASSIGNED',
                    },
                    include: {
                        base: {
                            select: { id: true, name: true, code: true }
                        },
                        asset: {
                            select: { id: true, name: true, code: true, category: true }
                        },
                        user: {
                            select: { id: true, name: true, email: true }
                        }
                    },
                });
                await tx.inventory.update({
                    where: {
                        baseId_assetId: {
                            baseId,
                            assetId,
                        }
                    },
                    data: {
                        availableCount: {
                            decrement: quantity
                        }
                    }
                });
                await tx.auditLog.create({
                    data: {
                        userId: req.user.id,
                        action: 'CREATE',
                        resource: 'ASSIGNMENT',
                        resourceId: assignment.id,
                        newValues: JSON.stringify({
                            baseId,
                            assetId,
                            assignedTo,
                            quantity,
                            description,
                        }),
                    }
                });
                return assignment;
            });
            res.status(201).json({ assignment: result });
        }
        catch (error) {
            console.error('Error creating assignment:', error);
            res.status(500).json({ error: 'Failed to create assignment' });
        }
    },
    async returnAsset(req, res) {
        try {
            const { id } = req.params;
            const { returnCondition, notes } = req.body;
            const assignment = await prisma.assetAssignment.findUnique({
                where: { id },
                include: {
                    base: true,
                    asset: true,
                }
            });
            if (!assignment) {
                return res.status(404).json({ error: 'Assignment not found' });
            }
            if (assignment.status !== 'ASSIGNED') {
                return res.status(400).json({ error: 'Asset is not currently assigned' });
            }
            if (req.user?.role !== enums_1.UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Access denied' });
            }
            const result = await prisma.$transaction(async (tx) => {
                const updatedAssignment = await tx.assetAssignment.update({
                    where: { id },
                    data: {
                        status: 'RETURNED',
                        returnedAt: new Date(),
                        description: assignment.description ?
                            `${assignment.description}\n\nReturned: ${notes || 'No notes'}` :
                            `Returned: ${notes || 'No notes'}`,
                    },
                    include: {
                        base: {
                            select: { id: true, name: true, code: true }
                        },
                        asset: {
                            select: { id: true, name: true, code: true, category: true }
                        },
                        user: {
                            select: { id: true, name: true, email: true }
                        }
                    },
                });
                await tx.inventory.update({
                    where: {
                        baseId_assetId: {
                            baseId: assignment.baseId,
                            assetId: assignment.assetId,
                        }
                    },
                    data: {
                        availableCount: {
                            increment: assignment.quantity
                        }
                    }
                });
                await tx.auditLog.create({
                    data: {
                        userId: req.user.id,
                        action: 'UPDATE',
                        resource: 'ASSIGNMENT',
                        resourceId: assignment.id,
                        oldValues: JSON.stringify({ status: 'ASSIGNED' }),
                        newValues: JSON.stringify({
                            status: 'RETURNED',
                            returnCondition,
                            notes
                        }),
                    }
                });
                return updatedAssignment;
            });
            res.json({ assignment: result });
        }
        catch (error) {
            console.error('Error returning asset:', error);
            res.status(500).json({ error: 'Failed to return asset' });
        }
    },
    async update(req, res) {
        try {
            const { id } = req.params;
            const { status, description } = req.body;
            const assignment = await prisma.assetAssignment.findUnique({
                where: { id }
            });
            if (!assignment) {
                return res.status(404).json({ error: 'Assignment not found' });
            }
            if (req.user?.role !== enums_1.UserRole.ADMIN && assignment.baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Access denied' });
            }
            const updatedAssignment = await prisma.assetAssignment.update({
                where: { id },
                data: {
                    ...(status && { status }),
                    ...(description && { description }),
                },
                include: {
                    base: {
                        select: { id: true, name: true, code: true }
                    },
                    asset: {
                        select: { id: true, name: true, code: true, category: true }
                    },
                    user: {
                        select: { id: true, name: true, email: true }
                    }
                },
            });
            await prisma.auditLog.create({
                data: {
                    userId: req.user.id,
                    action: 'UPDATE',
                    resource: 'ASSIGNMENT',
                    resourceId: id,
                    oldValues: JSON.stringify({
                        status: assignment.status,
                        description: assignment.description,
                    }),
                    newValues: JSON.stringify({ status, description }),
                }
            });
            res.json({ assignment: updatedAssignment });
        }
        catch (error) {
            console.error('Error updating assignment:', error);
            res.status(500).json({ error: 'Failed to update assignment' });
        }
    },
};
//# sourceMappingURL=assignmentController.js.map