import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { TransferStatus, TransactionType, UserRole } from '../types/enums';
import { CreateTransferRequest, UpdateTransferStatusRequest } from '../types/api';

// Extend Request type to include user
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    baseId: string;
  };
}

const prisma = new PrismaClient();

export const transferController = {
  // GET /api/transfers - List transfers with filtering and pagination
  async getAll(req: AuthenticatedRequest, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        baseId,
        status,
        startDate,
        endDate,
      } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const user = req.user!;

      // Build where clause based on user role and filters
      let whereClause: any = {};

      // Role-based filtering
      if (user.role === UserRole.BASE_COMMANDER || user.role === UserRole.LOGISTICS_OFFICER) {
        // Can only see transfers involving their base
        whereClause.OR = [
          { fromBaseId: user.baseId },
          { toBaseId: user.baseId },
        ];
      }

      // Additional filters
      if (baseId) {
        whereClause.OR = [
          { fromBaseId: baseId },
          { toBaseId: baseId },
        ];
      }

      if (status) {
        whereClause.status = status;
      }

      if (startDate || endDate) {
        whereClause.requestedAt = {};
        if (startDate) {
          whereClause.requestedAt.gte = new Date(startDate as string);
        }
        if (endDate) {
          whereClause.requestedAt.lte = new Date(endDate as string);
        }
      }

      // Get transfers with relations
      const [transfers, total] = await Promise.all([
        prisma.transfer.findMany({
          where: whereClause,
          skip,
          take: Number(limit),
          orderBy: { requestedAt: 'desc' },
          include: {
            fromBase: {
              select: { id: true, name: true, code: true },
            },
            toBase: {
              select: { id: true, name: true, code: true },
            },
            items: {
              include: {
                asset: {
                  select: { id: true, name: true, code: true, category: true },
                },
              },
            },
          },
        }),
        prisma.transfer.count({ where: whereClause }),
      ]);

      const pages = Math.ceil(total / Number(limit));

      res.json({
        transfers,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages,
        },
      });
    } catch (error) {
      console.error('Error fetching transfers:', error);
      res.status(500).json({ error: 'Failed to fetch transfers' });
    }
  },

  // GET /api/transfers/:id - Get specific transfer
  async getById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const user = req.user!;

      const transfer = await prisma.transfer.findUnique({
        where: { id },
        include: {
          fromBase: {
            select: { id: true, name: true, code: true, location: true },
          },
          toBase: {
            select: { id: true, name: true, code: true, location: true },
          },
          items: {
            include: {
              asset: {
                select: { id: true, name: true, code: true, category: true, unitPrice: true },
              },
            },
          },
        },
      });

      if (!transfer) {
        return res.status(404).json({ error: 'Transfer not found' });
      }

      // Check authorization
      if (user.role !== UserRole.ADMIN) {
        const hasAccess = transfer.fromBaseId === user.baseId || transfer.toBaseId === user.baseId;
        if (!hasAccess) {
          return res.status(403).json({ error: 'Access denied' });
        }
      }

      res.json(transfer);
    } catch (error) {
      console.error('Error fetching transfer:', error);
      res.status(500).json({ error: 'Failed to fetch transfer' });
    }
  },

  // POST /api/transfers - Create new transfer request
  async create(req: AuthenticatedRequest, res: Response) {
    try {
      const { fromBaseId, toBaseId, description, items }: CreateTransferRequest = req.body;
      const user = req.user!;

      // Validate user can create transfers from the specified base
      if (user.role !== UserRole.ADMIN && user.baseId !== fromBaseId) {
        return res.status(403).json({ error: 'Can only create transfers from your assigned base' });
      }

      // Validate inventory availability
      for (const item of items) {
        const inventory = await prisma.inventory.findUnique({
          where: {
            baseId_assetId: {
              baseId: fromBaseId,
              assetId: item.assetId,
            },
          },
        });

        if (!inventory || inventory.availableCount < item.quantity) {
          const asset = await prisma.asset.findUnique({
            where: { id: item.assetId },
            select: { name: true },
          });
          return res.status(400).json({
            error: `Insufficient inventory for ${asset?.name || 'asset'}. Available: ${inventory?.availableCount || 0}, Requested: ${item.quantity}`,
          });
        }
      }

      // Create transfer with items in a transaction
      const transfer = await prisma.$transaction(async (tx) => {
        // Create transfer
        const newTransfer = await tx.transfer.create({
          data: {
            fromBaseId,
            toBaseId,
            description,
            requestedById: user.id,
            status: TransferStatus.PENDING,
          },
        });

        // Create transfer items
        const transferItems = await Promise.all(
          items.map((item) =>
            tx.transferItem.create({
              data: {
                transferId: newTransfer.id,
                assetId: item.assetId,
                quantity: item.quantity,
              },
            })
          )
        );

        // Reserve inventory (reduce available count but not current balance)
        for (const item of items) {
          await tx.inventory.update({
            where: {
              baseId_assetId: {
                baseId: fromBaseId,
                assetId: item.assetId,
              },
            },
            data: {
              availableCount: {
                decrement: item.quantity,
              },
            },
          });
        }

        return newTransfer;
      });

      // Fetch complete transfer data for response
      const completeTransfer = await prisma.transfer.findUnique({
        where: { id: transfer.id },
        include: {
          fromBase: {
            select: { id: true, name: true, code: true },
          },
          toBase: {
            select: { id: true, name: true, code: true },
          },
          items: {
            include: {
              asset: {
                select: { id: true, name: true, code: true, category: true },
              },
            },
          },
        },
      });

      res.status(201).json(completeTransfer);
    } catch (error) {
      console.error('Error creating transfer:', error);
      res.status(500).json({ error: 'Failed to create transfer' });
    }
  },

  // PUT /api/transfers/:id/status - Update transfer status (approve/reject/complete)
  async updateStatus(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { status }: UpdateTransferStatusRequest = req.body;
      const user = req.user!;

      const transfer = await prisma.transfer.findUnique({
        where: { id },
        include: {
          fromBase: {
            select: { id: true, name: true },
          },
          toBase: {
            select: { id: true, name: true },
          },
          items: {
            include: {
              asset: {
                select: { id: true, name: true },
              },
            },
          },
        },
      });

      if (!transfer) {
        return res.status(404).json({ error: 'Transfer not found' });
      }

      // Validate status transition
      if (transfer.status === TransferStatus.COMPLETED || transfer.status === TransferStatus.CANCELLED) {
        return res.status(400).json({ error: 'Cannot modify completed or cancelled transfer' });
      }

      // Role-based authorization for status updates
      if (status === TransferStatus.APPROVED || status === TransferStatus.CANCELLED) {
        if (user.role !== UserRole.ADMIN && user.role !== UserRole.BASE_COMMANDER) {
          return res.status(403).json({ error: 'Insufficient permissions to approve/cancel transfers' });
        }
        
        // Base commanders can only approve transfers to their base
        if (user.role === UserRole.BASE_COMMANDER && user.baseId !== transfer.toBaseId) {
          return res.status(403).json({ error: 'Can only approve transfers to your base' });
        }
      }

      // Update transfer status and handle inventory changes
      const updatedTransfer = await prisma.$transaction(async (tx) => {
        let updateData: any = { status };

        if (status === TransferStatus.APPROVED) {
          updateData.approvedById = user.id;
          updateData.approvedAt = new Date();
          updateData.status = TransferStatus.IN_TRANSIT; // Auto-transition to in transit
        } else if (status === TransferStatus.COMPLETED) {
          updateData.completedAt = new Date();
          
          // Complete the inventory transfer
          for (const item of transfer.items) {
            // Remove from source base
            await tx.inventory.update({
              where: {
                baseId_assetId: {
                  baseId: transfer.fromBaseId,
                  assetId: item.assetId,
                },
              },
              data: {
                currentBalance: {
                  decrement: item.quantity,
                },
                // availableCount already decremented during creation
              },
            });

            // Add to destination base
            await tx.inventory.upsert({
              where: {
                baseId_assetId: {
                  baseId: transfer.toBaseId,
                  assetId: item.assetId,
                },
              },
              update: {
                currentBalance: {
                  increment: item.quantity,
                },
                availableCount: {
                  increment: item.quantity,
                },
              },
              create: {
                baseId: transfer.toBaseId,
                assetId: item.assetId,
                openingBalance: item.quantity,
                currentBalance: item.quantity,
                assignedCount: 0,
                availableCount: item.quantity,
              },
            });

            // Create transaction records
            await tx.transaction.create({
              data: {
                type: TransactionType.TRANSFER_OUT,
                assetId: item.assetId,
                baseId: transfer.fromBaseId,
                quantity: -item.quantity,
                referenceId: transfer.id,
                description: `Transfer to ${transfer.toBase?.name}`,
                createdById: user.id,
                approvedById: transfer.approvedById,
              },
            });

            await tx.transaction.create({
              data: {
                type: TransactionType.TRANSFER_IN,
                assetId: item.assetId,
                baseId: transfer.toBaseId,
                quantity: item.quantity,
                referenceId: transfer.id,
                description: `Transfer from ${transfer.fromBase?.name}`,
                createdById: user.id,
                approvedById: transfer.approvedById,
              },
            });
          }
        } else if (status === TransferStatus.CANCELLED) {
          // Restore available inventory
          for (const item of transfer.items) {
            await tx.inventory.update({
              where: {
                baseId_assetId: {
                  baseId: transfer.fromBaseId,
                  assetId: item.assetId,
                },
              },
              data: {
                availableCount: {
                  increment: item.quantity,
                },
              },
            });
          }
        }

        return await tx.transfer.update({
          where: { id },
          data: updateData,
          include: {
            fromBase: {
              select: { id: true, name: true, code: true },
            },
            toBase: {
              select: { id: true, name: true, code: true },
            },
            items: {
              include: {
                asset: {
                  select: { id: true, name: true, code: true, category: true },
                },
              },
            },
          },
        });
      });

      res.json(updatedTransfer);
    } catch (error) {
      console.error('Error updating transfer status:', error);
      res.status(500).json({ error: 'Failed to update transfer status' });
    }
  },

  // DELETE /api/transfers/:id - Cancel transfer (only if pending)
  async delete(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const user = req.user!;

      const transfer = await prisma.transfer.findUnique({
        where: { id },
        include: {
          items: true,
        },
      });

      if (!transfer) {
        return res.status(404).json({ error: 'Transfer not found' });
      }

      if (transfer.status !== TransferStatus.PENDING) {
        return res.status(400).json({ error: 'Can only cancel pending transfers' });
      }

      // Authorization check
      if (user.role !== UserRole.ADMIN && user.baseId !== transfer.fromBaseId) {
        return res.status(403).json({ error: 'Can only cancel transfers from your base' });
      }

      // Cancel transfer and restore inventory
      await prisma.$transaction(async (tx) => {
        // Restore available inventory
        for (const item of transfer.items) {
          await tx.inventory.update({
            where: {
              baseId_assetId: {
                baseId: transfer.fromBaseId,
                assetId: item.assetId,
              },
            },
            data: {
              availableCount: {
                increment: item.quantity,
              },
            },
          });
        }

        // Delete transfer items
        await tx.transferItem.deleteMany({
          where: { transferId: id },
        });

        // Delete transfer
        await tx.transfer.delete({
          where: { id },
        });
      });

      res.json({ message: 'Transfer cancelled successfully' });
    } catch (error) {
      console.error('Error cancelling transfer:', error);
      res.status(500).json({ error: 'Failed to cancel transfer' });
    }
  },
};
