"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dashboardController = void 0;
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const prisma = new client_1.PrismaClient();
exports.dashboardController = {
    getOverview: async (req, res) => {
        const { baseId, startDate, endDate } = req.query;
        const user = req.user;
        let baseFilter = {};
        if (user.role !== enums_1.UserRole.ADMIN) {
            baseFilter = { id: user.baseId };
        }
        else if (baseId) {
            baseFilter = { id: baseId };
        }
        const dateFilter = {};
        if (startDate || endDate) {
            dateFilter.createdAt = {};
            if (startDate)
                dateFilter.createdAt.gte = new Date(startDate);
            if (endDate)
                dateFilter.createdAt.lte = new Date(endDate);
        }
        const inventorySummary = await prisma.inventory.aggregate({
            where: { base: baseFilter },
            _sum: {
                openingBalance: true,
                currentBalance: true,
                assignedCount: true,
                availableCount: true,
            },
        });
        const transactionSummary = await prisma.transaction.groupBy({
            by: ['type'],
            where: {
                ...dateFilter,
                ...(Object.keys(baseFilter).length > 0 && {
                    asset: {
                        inventory: {
                            some: { base: baseFilter }
                        }
                    }
                })
            },
            _sum: {
                quantity: true,
                totalValue: true,
            },
        });
        const purchases = transactionSummary.find((t) => t.type === enums_1.TransactionType.PURCHASE)?._sum.quantity || 0;
        const transfersIn = transactionSummary.find((t) => t.type === enums_1.TransactionType.TRANSFER_IN)?._sum.quantity || 0;
        const transfersOut = transactionSummary.find((t) => t.type === enums_1.TransactionType.TRANSFER_OUT)?._sum.quantity || 0;
        const assignments = transactionSummary.find((t) => t.type === enums_1.TransactionType.ASSIGNMENT)?._sum.quantity || 0;
        const expenditures = transactionSummary.find((t) => t.type === enums_1.TransactionType.EXPENDITURE)?._sum.quantity || 0;
        const netMovement = purchases + transfersIn - transfersOut - assignments - expenditures;
        const bases = await prisma.base.findMany({
            where: baseFilter,
            select: {
                id: true,
                name: true,
                code: true,
                location: true,
            },
        });
        res.json({
            overview: {
                openingBalance: inventorySummary._sum.openingBalance || 0,
                currentBalance: inventorySummary._sum.currentBalance || 0,
                assignedCount: inventorySummary._sum.assignedCount || 0,
                availableCount: inventorySummary._sum.availableCount || 0,
                netMovement,
                purchases,
                transfersIn,
                transfersOut,
                assignments,
                expenditures,
            },
            bases,
            dateRange: {
                startDate: startDate || null,
                endDate: endDate || null,
            },
        });
    },
    getMetrics: async (req, res) => {
        const { baseId, startDate, endDate } = req.query;
        const user = req.user;
        let baseFilter = {};
        if (user.role !== enums_1.UserRole.ADMIN) {
            baseFilter = { id: user.baseId };
        }
        else if (baseId) {
            baseFilter = { id: baseId };
        }
        const dateFilter = {};
        if (startDate || endDate) {
            dateFilter.createdAt = {};
            if (startDate)
                dateFilter.createdAt.gte = new Date(startDate);
            if (endDate)
                dateFilter.createdAt.lte = new Date(endDate);
        }
        const categoryMetrics = await prisma.inventory.groupBy({
            by: ['assetId'],
            where: { base: baseFilter },
            _sum: {
                currentBalance: true,
                assignedCount: true,
                availableCount: true,
            },
        });
        const assetIds = categoryMetrics.map(metric => metric.assetId);
        const assets = await prisma.asset.findMany({
            where: { id: { in: assetIds } },
            select: {
                id: true,
                name: true,
                category: true,
            },
        });
        const categoryMetricsWithAssets = categoryMetrics.map(metric => ({
            ...metric,
            asset: assets.find(asset => asset.id === metric.assetId),
        }));
        const recentTransactions = await prisma.transaction.findMany({
            where: {
                ...dateFilter,
                ...(Object.keys(baseFilter).length > 0 && {
                    asset: {
                        inventory: {
                            some: { base: baseFilter }
                        }
                    }
                })
            },
            orderBy: { createdAt: 'desc' },
            take: 10,
            include: {
                asset: {
                    select: {
                        name: true,
                        category: true,
                    },
                },
                createdBy: {
                    select: {
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
        res.json({
            categoryMetrics: categoryMetricsWithAssets,
            recentTransactions,
        });
    },
    getNetMovementDetails: async (req, res) => {
        const { baseId } = req.params;
        const { startDate, endDate } = req.query;
        const user = req.user;
        if (user.role !== enums_1.UserRole.ADMIN && user.baseId !== baseId) {
            res.status(403).json({ error: 'Access denied to this base' });
            return;
        }
        const dateFilter = {};
        if (startDate || endDate) {
            dateFilter.createdAt = {};
            if (startDate)
                dateFilter.createdAt.gte = new Date(startDate);
            if (endDate)
                dateFilter.createdAt.lte = new Date(endDate);
        }
        const purchases = await prisma.transaction.findMany({
            where: {
                type: enums_1.TransactionType.PURCHASE,
                ...dateFilter,
                asset: {
                    inventory: {
                        some: { baseId }
                    }
                }
            },
            include: {
                asset: { select: { name: true, category: true } },
                createdBy: { select: { firstName: true, lastName: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
        const transfersIn = await prisma.transaction.findMany({
            where: {
                type: enums_1.TransactionType.TRANSFER_IN,
                ...dateFilter,
                asset: {
                    inventory: {
                        some: { baseId }
                    }
                }
            },
            include: {
                asset: { select: { name: true, category: true } },
                createdBy: { select: { firstName: true, lastName: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
        const transfersOut = await prisma.transaction.findMany({
            where: {
                type: enums_1.TransactionType.TRANSFER_OUT,
                ...dateFilter,
                asset: {
                    inventory: {
                        some: { baseId }
                    }
                }
            },
            include: {
                asset: { select: { name: true, category: true } },
                createdBy: { select: { firstName: true, lastName: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
        res.json({
            purchases: {
                count: purchases.length,
                totalQuantity: purchases.reduce((sum, t) => sum + t.quantity, 0),
                totalValue: purchases.reduce((sum, t) => sum + (t.totalValue?.toNumber() || 0), 0),
                transactions: purchases,
            },
            transfersIn: {
                count: transfersIn.length,
                totalQuantity: transfersIn.reduce((sum, t) => sum + t.quantity, 0),
                transactions: transfersIn,
            },
            transfersOut: {
                count: transfersOut.length,
                totalQuantity: transfersOut.reduce((sum, t) => sum + t.quantity, 0),
                transactions: transfersOut,
            },
        });
    },
    getRecentActivities: async (req, res) => {
        const { baseId, page = 1, limit = 10 } = req.query;
        const user = req.user;
        let baseFilter = {};
        if (user.role !== enums_1.UserRole.ADMIN) {
            baseFilter = { baseId: user.baseId };
        }
        else if (baseId) {
            baseFilter = { baseId: baseId };
        }
        const skip = (Number(page) - 1) * Number(limit);
        const activities = await prisma.transaction.findMany({
            where: baseFilter,
            orderBy: { createdAt: 'desc' },
            skip,
            take: Number(limit),
            include: {
                asset: {
                    select: {
                        name: true,
                        category: true,
                    },
                },
                createdBy: {
                    select: {
                        firstName: true,
                        lastName: true,
                        username: true,
                    },
                },
            },
        });
        const totalCount = await prisma.transaction.count({
            where: baseFilter,
        });
        res.json({
            activities,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / Number(limit)),
            },
        });
    },
    getInventoryAlerts: async (req, res) => {
        const { baseId } = req.query;
        const user = req.user;
        let baseFilter = {};
        if (user.role !== enums_1.UserRole.ADMIN) {
            baseFilter = { id: user.baseId };
        }
        else if (baseId) {
            baseFilter = { id: baseId };
        }
        const lowStockItems = await prisma.inventory.findMany({
            where: {
                base: baseFilter,
                availableCount: {
                    lt: 5,
                },
            },
            include: {
                asset: {
                    select: {
                        name: true,
                        category: true,
                    },
                },
                base: {
                    select: {
                        name: true,
                        code: true,
                    },
                },
            },
        });
        const overdueAssignments = await prisma.assetAssignment.findMany({
            where: {
                base: baseFilter,
                assignedAt: {
                    lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                },
                returnedAt: null,
            },
            include: {
                asset: {
                    select: {
                        name: true,
                        category: true,
                    },
                },
                base: {
                    select: {
                        name: true,
                        code: true,
                    },
                },
            },
        });
        res.json({
            alerts: {
                lowStock: lowStockItems,
                overdueAssignments,
            },
            summary: {
                lowStockCount: lowStockItems.length,
                overdueAssignmentsCount: overdueAssignments.length,
            },
        });
    },
    getChartsData: async (req, res) => {
        const { baseId, startDate, endDate } = req.query;
        const user = req.user;
        let baseFilter = {};
        if (user.role !== enums_1.UserRole.ADMIN) {
            baseFilter = { baseId: user.baseId };
        }
        else if (baseId) {
            baseFilter = { baseId: baseId };
        }
        const dateFilter = {};
        if (startDate || endDate) {
            dateFilter.createdAt = {};
            if (startDate)
                dateFilter.createdAt.gte = new Date(startDate);
            if (endDate)
                dateFilter.createdAt.lte = new Date(endDate);
        }
        const transactionTrends = await prisma.transaction.groupBy({
            by: ['type', 'createdAt'],
            where: { ...baseFilter, ...dateFilter },
            _sum: {
                quantity: true,
                totalValue: true,
            },
            orderBy: {
                createdAt: 'asc',
            },
        });
        const assetDistribution = await prisma.inventory.groupBy({
            by: ['assetId'],
            where: { base: { id: baseId || user.baseId } },
            _sum: {
                currentBalance: true,
            },
        });
        const distributionAssetIds = assetDistribution.map(dist => dist.assetId);
        const distributionAssets = await prisma.asset.findMany({
            where: { id: { in: distributionAssetIds } },
            select: {
                id: true,
                category: true,
            },
        });
        const assetDistributionWithAssets = assetDistribution.map(dist => ({
            ...dist,
            asset: distributionAssets.find(asset => asset.id === dist.assetId),
        }));
        res.json({
            transactionTrends,
            assetDistribution: assetDistributionWithAssets,
        });
    },
};
//# sourceMappingURL=dashboardController.js.map