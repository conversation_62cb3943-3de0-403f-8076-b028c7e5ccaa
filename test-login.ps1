# Test Login with Correct Credentials
Write-Host "🔐 Testing Login with Correct Credentials" -ForegroundColor Green

$baseUrl = "http://localhost:3001"

# Test credentials from seed script
$credentials = @(
    @{ email = "<EMAIL>"; password = "password123"; role = "Admin" },
    @{ email = "<EMAIL>"; password = "password123"; role = "Commander" },
    @{ email = "<EMAIL>"; password = "password123"; role = "Logistics" }
)

foreach ($cred in $credentials) {
    Write-Host ""
    Write-Host "Testing $($cred.role): $($cred.email)" -ForegroundColor Yellow
    
    try {
        $loginData = @{
            email = $cred.email
            password = $cred.password
        } | ConvertTo-Json

        $response = Invoke-WebRequest -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -UseBasicParsing
        $result = $response.Content | ConvertFrom-Json
        
        if ($result.token) {
            Write-Host "✅ SUCCESS: $($cred.role) login worked!" -ForegroundColor Green
            Write-Host "   Token: $($result.token.Substring(0,20))..." -ForegroundColor Gray
        } else {
            Write-Host "❌ FAILED: No token received" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorContent = $reader.ReadToEnd()
            Write-Host "   Error details: $errorContent" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "🎯 CORRECT LOGIN CREDENTIALS:" -ForegroundColor Cyan
Write-Host "• Admin: <EMAIL> / password123" -ForegroundColor White
Write-Host "• Commander: <EMAIL> / password123" -ForegroundColor White  
Write-Host "• Logistics: <EMAIL> / password123" -ForegroundColor White
Write-Host ""
Write-Host "Use these credentials in the frontend at: http://localhost:5176" -ForegroundColor Green
