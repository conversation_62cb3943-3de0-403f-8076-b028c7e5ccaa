import { Router } from 'express';
import { authenticate, authorize, authorizeBase, authorizeResourceOwnership } from '../middleware/auth';
import { validate, validateQuery } from '../utils/validation';
import { purchaseSchemas, querySchemas } from '../utils/validation';
import { asyncHandler } from '../middleware/errorHandler';
import { UserRole } from '../types/enums';
import { purchaseController } from '../controllers/purchaseController';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/purchases - List purchases
router.get(
  '/',
  validateQuery(querySchemas.pagination.concat(querySchemas.baseFilter).concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(purchaseController.getAllPurchases)
);

// GET /api/purchases/:id - Get specific purchase
router.get(
  '/:id',
  authorizeResourceOwnership('purchase'),
  asyncHandler(purchaseController.getPurchaseById)
);

// POST /api/purchases - Create new purchase
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER, UserRole.LOGISTICS_OFFICER]),
  validate(purchaseSchemas.create),
  authorizeBase,
  asyncHandler(purchaseController.createPurchase)
);

// PUT /api/purchases/:id - Update purchase (before approval)
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER, UserRole.LOGISTICS_OFFICER]),
  validate(purchaseSchemas.create),
  authorizeResourceOwnership('purchase'),
  asyncHandler(purchaseController.updatePurchase)
);

// DELETE /api/purchases/:id - Delete purchase (before approval)
router.delete(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  authorizeResourceOwnership('purchase'),
  asyncHandler(purchaseController.deletePurchase)
);

export default router;
