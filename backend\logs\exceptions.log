{"date":"Tue Jun 24 2025 17:00:13 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":613772.031},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867287,"external":7822966,"heapTotal":195399680,"heapUsed":172775368,"rss":242946048},"pid":22624,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:00:13:013","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:01:01 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":613820.453},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867167,"external":7822846,"heapTotal":196448256,"heapUsed":171235768,"rss":243122176},"pid":22504,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:01:01:11","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:05:52 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614110.718},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867151,"external":7822830,"heapTotal":195399680,"heapUsed":172756680,"rss":242032640},"pid":36840,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:05:52:552","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:08 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614126.625},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867183,"external":7822862,"heapTotal":195923968,"heapUsed":171402664,"rss":247885824},"pid":23268,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:08:68","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:28 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614146.953},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867303,"external":7822982,"heapTotal":195661824,"heapUsed":172149736,"rss":242122752},"pid":14380,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:28:628","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:48 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614166.609},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867319,"external":7822998,"heapTotal":196640768,"heapUsed":171575144,"rss":242757632},"pid":25580,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:48:648","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:07:49 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614227.781},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867175,"external":7828341,"heapTotal":195661824,"heapUsed":163674728,"rss":242135040},"pid":24760,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:07:49:749","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:27:41 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615419.718},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867175,"external":7828341,"heapTotal":196448256,"heapUsed":163947504,"rss":249925632},"pid":27832,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:27:41:2741","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:28:11 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615450.093},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867199,"external":7822878,"heapTotal":195661824,"heapUsed":173058136,"rss":247889920},"pid":22532,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:28:11:2811","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:28:44 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615483.359},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867287,"external":7837440,"heapTotal":193040384,"heapUsed":165961808,"rss":246149120},"pid":9040,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:28:44:2844","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:41:09 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616227.89},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859015,"external":7829168,"heapTotal":205099008,"heapUsed":180961520,"rss":258465792},"pid":2020,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:41:09:419","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:41:49 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616267.656},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859063,"external":7829216,"heapTotal":204836864,"heapUsed":181430888,"rss":258949120},"pid":34488,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:41:49:4149","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:41:59 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616278.25},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859079,"external":7829232,"heapTotal":204836864,"heapUsed":181579232,"rss":258809856},"pid":13684,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:41:59:4159","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:42:10 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616289.296},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859039,"external":7829192,"heapTotal":204312576,"heapUsed":181725336,"rss":257699840},"pid":31288,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:42:10:4210","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:42:31 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616310.328},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859071,"external":7829224,"heapTotal":205099008,"heapUsed":180797712,"rss":259284992},"pid":27184,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:42:31:4231","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:42:42 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616321.125},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859071,"external":7829224,"heapTotal":205099008,"heapUsed":180736552,"rss":259227648},"pid":15580,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:42:42:4242","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:42:53 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616331.671},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859063,"external":7829216,"heapTotal":205099008,"heapUsed":180782728,"rss":259305472},"pid":5492,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:42:53:4253","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:43:05 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616343.671},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859119,"external":7829272,"heapTotal":205361152,"heapUsed":180777056,"rss":258887680},"pid":5756,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:43:05:435","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:43:16 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616354.921},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859095,"external":7829248,"heapTotal":204050432,"heapUsed":181717816,"rss":258101248},"pid":28100,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:43:16:4316","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:43:27 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616366.5},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859071,"external":7829224,"heapTotal":204312576,"heapUsed":181786664,"rss":257871872},"pid":2064,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:43:27:4327","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:43:41 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616379.687},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859103,"external":7829256,"heapTotal":204836864,"heapUsed":181417632,"rss":261509120},"pid":13996,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:43:41:4341","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:43:54 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616393.406},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859087,"external":7829240,"heapTotal":205099008,"heapUsed":180567776,"rss":260198400},"pid":34244,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:43:54:4354","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:44:07 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[2305,2305,2305,2305,6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616406.375},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859023,"external":7829176,"heapTotal":204574720,"heapUsed":181365992,"rss":258899968},"pid":31736,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'UserRole'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m20\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'EquipmentCategory'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                   ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'TransferStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"@prisma/client\"' has no exported member 'AssetStatus'.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '@prisma/client';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:44:07:447","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:44:20 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616419.453},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859039,"external":7829192,"heapTotal":204312576,"heapUsed":180621216,"rss":257613824},"pid":9116,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:44:20:4420","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:45:41 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616499.812},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4858983,"external":7829136,"heapTotal":205099008,"heapUsed":179865968,"rss":258007040},"pid":23160,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:45:41:4541","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:48:32 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616671.453},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4858927,"external":7829080,"heapTotal":204836864,"heapUsed":180083264,"rss":258031616},"pid":8752,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:48:32:4832","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:48:42 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616681.25},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859031,"external":7829184,"heapTotal":204574720,"heapUsed":180309360,"rss":259035136},"pid":33428,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:48:42:4842","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:48:53 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616691.546},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859111,"external":7829264,"heapTotal":204836864,"heapUsed":180085848,"rss":259276800},"pid":4172,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:48:53:4853","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:49:05 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616703.578},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859159,"external":7829312,"heapTotal":203788288,"heapUsed":181954720,"rss":258072576},"pid":1288,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:49:05:495","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:49:20 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616719.078},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859055,"external":7829208,"heapTotal":204050432,"heapUsed":180428248,"rss":257519616},"pid":22804,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:49:20:4920","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:49:43 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616741.593},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859015,"external":7829168,"heapTotal":205099008,"heapUsed":180629864,"rss":259448832},"pid":31992,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:49:43:4943","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:49:54 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616752.812},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859151,"external":7829304,"heapTotal":205361152,"heapUsed":180187128,"rss":258723840},"pid":16732,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:49:54:4954","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:51:44 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616863.281},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859111,"external":7829264,"heapTotal":204836864,"heapUsed":180011936,"rss":258453504},"pid":31184,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:51:44:5144","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:52:37 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616916.406},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859031,"external":7829184,"heapTotal":205099008,"heapUsed":179948504,"rss":258523136},"pid":17884,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:52:37:5237","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:53:52 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":616991.187},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859111,"external":7829264,"heapTotal":204836864,"heapUsed":179967736,"rss":259497984},"pid":8624,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:53:52:5352","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:56:31 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":617150.015},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867151,"external":7828317,"heapTotal":204836864,"heapUsed":175067072,"rss":258211840},"pid":23872,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/validation.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m55\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'AssetStatus' is declared but its value is never read.\r\n\r\n\u001b[7m2\u001b[0m import { UserRole, EquipmentCategory, TransferStatus, AssetStatus } from '../types/enums';\r\n\u001b[7m \u001b[0m \u001b[91m                                                      ~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:56:31:5631","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 19:17:35 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","os":{"loadavg":[0,0,0],"uptime":622013.75},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4859023,"external":7916647,"heapTotal":92172288,"heapUsed":62541176,"rss":144523264},"pid":26652,"uid":null,"version":"v22.13.1"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","timestamp":"2025-06-24 19:17:35:1735","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts","function":null,"line":153,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1562,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false}]}
{"date":"Tue Jun 24 2025 19:18:33 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","os":{"loadavg":[0,0,0],"uptime":622072.39},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4912253,"external":7887063,"heapTotal":90075136,"heapUsed":65473096,"rss":141656064},"pid":36352,"uid":null,"version":"v22.13.1"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","timestamp":"2025-06-24 19:18:33:1833","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts","function":null,"line":153,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1562,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false}]}
{"date":"Tue Jun 24 2025 19:22:32 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","os":{"loadavg":[0,0,0],"uptime":622310.906},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4884347,"external":7859157,"heapTotal":89812992,"heapUsed":65165384,"rss":142045184},"pid":33492,"uid":null,"version":"v22.13.1"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","timestamp":"2025-06-24 19:22:32:2232","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts","function":null,"line":153,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1562,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false}]}
{"date":"Tue Jun 24 2025 19:23:00 GMT+0530 (India Standard Time)","error":{"address":"::","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::3001\nError: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","os":{"loadavg":[0,0,0],"uptime":622339.453},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4884371,"external":7859181,"heapTotal":89812992,"heapUsed":65036080,"rss":141926400},"pid":30608,"uid":null,"version":"v22.13.1"},"stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts:153:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)","timestamp":"2025-06-24 19:23:00:230","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\express\\lib\\application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":5,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts","function":null,"line":153,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1562,"method":"_compile","native":false},{"column":23,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false}]}
