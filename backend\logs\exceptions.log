{"date":"Tue Jun 24 2025 17:00:13 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":613772.031},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867287,"external":7822966,"heapTotal":195399680,"heapUsed":172775368,"rss":242946048},"pid":22624,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:00:13:013","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:01:01 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":613820.453},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867167,"external":7822846,"heapTotal":196448256,"heapUsed":171235768,"rss":243122176},"pid":22504,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:01:01:11","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:05:52 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614110.718},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867151,"external":7822830,"heapTotal":195399680,"heapUsed":172756680,"rss":242032640},"pid":36840,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:05:52:552","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:08 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614126.625},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867183,"external":7822862,"heapTotal":195923968,"heapUsed":171402664,"rss":247885824},"pid":23268,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:08:68","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:28 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614146.953},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867303,"external":7822982,"heapTotal":195661824,"heapUsed":172149736,"rss":242122752},"pid":14380,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:28:628","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:06:48 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614166.609},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867319,"external":7822998,"heapTotal":196640768,"heapUsed":171575144,"rss":242757632},"pid":25580,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:06:48:648","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:07:49 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":614227.781},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867175,"external":7828341,"heapTotal":195661824,"heapUsed":163674728,"rss":242135040},"pid":24760,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:07:49:749","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:27:41 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615419.718},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867175,"external":7828341,"heapTotal":196448256,"heapUsed":163947504,"rss":249925632},"pid":27832,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:27:41:2741","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:28:11 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615450.093},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867199,"external":7822878,"heapTotal":195661824,"heapUsed":173058136,"rss":247889920},"pid":22532,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:28:11:2811","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 17:28:44 GMT+0530 (India Standard Time)","error":{"diagnosticCodes":[6133,2375]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":615483.359},"process":{"argv":["C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend\\src\\server.ts"],"cwd":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4867287,"external":7837440,"heapTotal":193040384,"heapUsed":165961808,"rss":246149120},"pid":9040,"uid":null,"version":"v22.13.1"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m33\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS6133: \u001b[0m'resource' is declared but its value is never read.\r\n\r\n\u001b[7m33\u001b[0m   const resource = segments[1]; // 'api'\r\n\u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/middleware/auditLogger.ts\u001b[0m:\u001b[93m181\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2375: \u001b[0mType '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type '(Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput)' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n  Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n    Type '{ userId: string; action: string; resource: string; resourceId: string | undefined; oldValues: any; newValues: any; ipAddress: string; userAgent: string; }' is not assignable to type 'AuditLogUncheckedCreateInput' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.\r\n      Types of property 'resourceId' are incompatible.\r\n        Type 'string | undefined' is not assignable to type 'string | null'.\r\n          Type 'undefined' is not assignable to type 'string | null'.\r\n\r\n\u001b[7m181\u001b[0m       data: {\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~\u001b[0m\r\n\r\n  \u001b[96m../node_modules/.prisma/client/index.d.ts\u001b[0m:\u001b[93m13459\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m13459\u001b[0m     data: XOR<AuditLogCreateInput, AuditLogUncheckedCreateInput>\r\n    \u001b[7m     \u001b[0m \u001b[96m    ~~~~\u001b[0m\r\n    The expected type comes from property 'data' which is declared here on type '{ select?: AuditLogSelect<DefaultArgs> | null; include?: AuditLogInclude<DefaultArgs> | null; data: (Without<AuditLogCreateInput, AuditLogUncheckedCreateInput> & AuditLogUncheckedCreateInput) | (Without<...> & AuditLogCreateInput); }'\r\n\n    at createTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1699:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-24 17:28:44:2844","trace":[{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1699,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\OneDrive\\Desktop\\Military Management System\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1313,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1123,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
