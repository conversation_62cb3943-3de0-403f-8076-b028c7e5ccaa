import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { UserRole } from '../types/enums';

const prisma = new PrismaClient();

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    role: string;
    baseId: string;
  };
}

export const expenditureController = {
  // Get all expenditures (transactions with expenditure types)
  async getAll(req: AuthenticatedRequest, res: Response) {
    try {
      const {
        page = 1,
        limit = 10,
        baseId,
        assetId,
        type,
        startDate,
        endDate,
      } = req.query;

      const skip = (Number(page) - 1) * Number(limit);
      const take = Number(limit);

      // Build where clause based on user role and filters
      let whereClause: any = {
        type: {
          in: ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL']
        }
      };

      // Role-based filtering
      if (req.user?.role === UserRole.BASE_COMMANDER || req.user?.role === UserRole.LOGISTICS_OFFICER) {
        whereClause.baseId = req.user.baseId;
      }

      // Apply additional filters
      if (baseId) whereClause.baseId = baseId as string;
      if (assetId) whereClause.assetId = assetId as string;
      if (type) whereClause.type = type as string;

      // Date range filtering
      if (startDate || endDate) {
        whereClause.createdAt = {};
        if (startDate) whereClause.createdAt.gte = new Date(startDate as string);
        if (endDate) whereClause.createdAt.lte = new Date(endDate as string);
      }

      const [expenditures, total] = await Promise.all([
        prisma.transaction.findMany({
          where: whereClause,
          include: {
            asset: {
              select: { id: true, name: true, code: true, category: true }
            },
            createdBy: {
              select: { id: true, name: true, email: true }
            },
            approvedBy: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take,
        }),
        prisma.transaction.count({ where: whereClause }),
      ]);

      const totalPages = Math.ceil(total / take);

      res.json({
        expenditures,
        pagination: {
          page: Number(page),
          limit: take,
          total,
          pages: totalPages,
        },
      });
    } catch (error) {
      console.error('Error fetching expenditures:', error);
      res.status(500).json({ error: 'Failed to fetch expenditures' });
    }
  },

  // Get expenditure by ID
  async getById(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;

      const expenditure = await prisma.transaction.findUnique({
        where: { id },
        include: {
          asset: {
            select: { id: true, name: true, code: true, category: true, description: true }
          },
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          approvedBy: {
            select: { id: true, name: true, email: true }
          }
        },
      });

      if (!expenditure) {
        return res.status(404).json({ error: 'Expenditure not found' });
      }

      // Check if it's actually an expenditure transaction
      const expenditureTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
      if (!expenditureTypes.includes(expenditure.type)) {
        return res.status(404).json({ error: 'Transaction is not an expenditure' });
      }

      // Check authorization
      if (req.user?.role !== UserRole.ADMIN && expenditure.baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      res.json({ expenditure });
    } catch (error) {
      console.error('Error fetching expenditure:', error);
      res.status(500).json({ error: 'Failed to fetch expenditure' });
    }
  },

  // Create new expenditure
  async create(req: AuthenticatedRequest, res: Response) {
    try {
      const { baseId, assetId, quantity, type, unitPrice, description, reason } = req.body;

      // Validate base access
      if (req.user?.role !== UserRole.ADMIN && baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Cannot create expenditures for other bases' });
      }

      // Validate expenditure type
      const validTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({ error: 'Invalid expenditure type' });
      }

      // Check if asset exists and has sufficient inventory
      const inventory = await prisma.inventory.findUnique({
        where: {
          baseId_assetId: {
            baseId,
            assetId,
          }
        },
        include: {
          asset: true
        }
      });

      if (!inventory) {
        return res.status(400).json({ error: 'Asset not available at this base' });
      }

      if (inventory.availableCount < quantity) {
        return res.status(400).json({ 
          error: `Insufficient inventory. Available: ${inventory.availableCount}, Requested: ${quantity}` 
        });
      }

      const totalValue = unitPrice ? unitPrice * quantity : null;

      // Create expenditure transaction and update inventory in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the expenditure transaction
        const expenditure = await tx.transaction.create({
          data: {
            type,
            baseId,
            assetId,
            quantity: -quantity, // Negative for expenditures
            unitPrice,
            totalValue,
            description: description || reason,
            createdById: req.user!.id,
            approvedById: req.user!.id, // Auto-approve for now
            approvedAt: new Date(),
          },
          include: {
            asset: {
              select: { id: true, name: true, code: true, category: true }
            },
            createdBy: {
              select: { id: true, name: true, email: true }
            },
            approvedBy: {
              select: { id: true, name: true, email: true }
            }
          },
        });

        // Update inventory - reduce available count
        await tx.inventory.update({
          where: {
            baseId_assetId: {
              baseId,
              assetId,
            }
          },
          data: {
            availableCount: {
              decrement: quantity
            }
          }
        });

        // Create audit log
        await tx.auditLog.create({
          data: {
            userId: req.user!.id,
            action: 'CREATE',
            resource: 'EXPENDITURE',
            resourceId: expenditure.id,
            newValues: JSON.stringify({
              type,
              baseId,
              assetId,
              quantity,
              unitPrice,
              totalValue,
              description,
              reason,
            }),
          }
        });

        return expenditure;
      });

      res.status(201).json({ expenditure: result });
    } catch (error) {
      console.error('Error creating expenditure:', error);
      res.status(500).json({ error: 'Failed to create expenditure' });
    }
  },

  // Update expenditure
  async update(req: AuthenticatedRequest, res: Response) {
    try {
      const { id } = req.params;
      const { description, unitPrice } = req.body;

      const expenditure = await prisma.transaction.findUnique({
        where: { id }
      });

      if (!expenditure) {
        return res.status(404).json({ error: 'Expenditure not found' });
      }

      // Check if it's actually an expenditure transaction
      const expenditureTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
      if (!expenditureTypes.includes(expenditure.type)) {
        return res.status(404).json({ error: 'Transaction is not an expenditure' });
      }

      // Check authorization
      if (req.user?.role !== UserRole.ADMIN && expenditure.baseId !== req.user?.baseId) {
        return res.status(403).json({ error: 'Access denied' });
      }

      const totalValue = unitPrice ? unitPrice * Math.abs(expenditure.quantity) : expenditure.totalValue;

      const updatedExpenditure = await prisma.transaction.update({
        where: { id },
        data: {
          ...(description && { description }),
          ...(unitPrice && { unitPrice, totalValue }),
        },
        include: {
          asset: {
            select: { id: true, name: true, code: true, category: true }
          },
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          approvedBy: {
            select: { id: true, name: true, email: true }
          }
        },
      });

      // Create audit log
      await prisma.auditLog.create({
        data: {
          userId: req.user!.id,
          action: 'UPDATE',
          resource: 'EXPENDITURE',
          resourceId: id,
          oldValues: JSON.stringify({
            description: expenditure.description,
            unitPrice: expenditure.unitPrice,
            totalValue: expenditure.totalValue,
          }),
          newValues: JSON.stringify({ description, unitPrice, totalValue }),
        }
      });

      res.json({ expenditure: updatedExpenditure });
    } catch (error) {
      console.error('Error updating expenditure:', error);
      res.status(500).json({ error: 'Failed to update expenditure' });
    }
  },

  // Get expenditure summary/statistics
  async getSummary(req: AuthenticatedRequest, res: Response) {
    try {
      const { baseId, startDate, endDate } = req.query;

      let whereClause: any = {
        type: {
          in: ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL']
        }
      };

      // Role-based filtering
      if (req.user?.role === UserRole.BASE_COMMANDER || req.user?.role === UserRole.LOGISTICS_OFFICER) {
        whereClause.baseId = req.user.baseId;
      }

      if (baseId) whereClause.baseId = baseId as string;

      // Date range filtering
      if (startDate || endDate) {
        whereClause.createdAt = {};
        if (startDate) whereClause.createdAt.gte = new Date(startDate as string);
        if (endDate) whereClause.createdAt.lte = new Date(endDate as string);
      }

      const [totalExpenditures, totalValue, expendituresByType] = await Promise.all([
        prisma.transaction.count({ where: whereClause }),
        prisma.transaction.aggregate({
          where: whereClause,
          _sum: { totalValue: true }
        }),
        prisma.transaction.groupBy({
          by: ['type'],
          where: whereClause,
          _count: { id: true },
          _sum: { quantity: true, totalValue: true }
        })
      ]);

      res.json({
        summary: {
          totalExpenditures,
          totalValue: totalValue._sum.totalValue || 0,
          expendituresByType: expendituresByType.map(item => ({
            type: item.type,
            count: item._count.id,
            totalQuantity: Math.abs(item._sum.quantity || 0),
            totalValue: item._sum.totalValue || 0,
          }))
        }
      });
    } catch (error) {
      console.error('Error fetching expenditure summary:', error);
      res.status(500).json({ error: 'Failed to fetch expenditure summary' });
    }
  },
};
