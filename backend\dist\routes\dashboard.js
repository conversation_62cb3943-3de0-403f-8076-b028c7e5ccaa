"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const dashboardController_1 = require("../controllers/dashboardController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/overview', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter.concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getOverview));
router.get('/metrics', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter.concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getMetrics));
router.get('/net-movement/:baseId', (0, validation_1.validateQuery)(validation_2.querySchemas.dateRange), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getNetMovementDetails));
router.get('/activities', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter.concat(validation_2.querySchemas.pagination)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getRecentActivities));
router.get('/alerts', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getInventoryAlerts));
router.get('/charts', (0, validation_1.validateQuery)(validation_2.querySchemas.baseFilter.concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(dashboardController_1.dashboardController.getChartsData));
exports.default = router;
//# sourceMappingURL=dashboard.js.map