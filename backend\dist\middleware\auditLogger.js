"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAuditLog = exports.auditLogger = void 0;
const client_1 = require("@prisma/client");
const logger_1 = require("../utils/logger");
const prisma = new client_1.PrismaClient();
const AUDITABLE_ACTIONS = [
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
];
const AUDITABLE_RESOURCES = [
    '/api/purchases',
    '/api/transfers',
    '/api/assignments',
    '/api/assets',
    '/api/inventory',
    '/api/users',
    '/api/bases',
];
const extractResourceInfo = (url, method) => {
    const segments = url.split('/').filter(Boolean);
    if (segments.length < 2)
        return null;
    const resource = segments[1];
    const resourceType = segments[2];
    const resourceId = segments[3];
    return {
        resource: resourceType,
        resourceId: resourceId || null,
        action: getActionFromMethod(method, !!resourceId),
    };
};
const getActionFromMethod = (method, hasId) => {
    switch (method) {
        case 'POST':
            return 'CREATE';
        case 'PUT':
        case 'PATCH':
            return 'UPDATE';
        case 'DELETE':
            return 'DELETE';
        case 'GET':
            return hasId ? 'VIEW' : 'LIST';
        default:
            return method;
    }
};
const captureRequestData = (req) => {
    const sensitiveFields = ['password', 'token', 'secret'];
    const data = { ...req.body };
    sensitiveFields.forEach(field => {
        if (data[field]) {
            data[field] = '[REDACTED]';
        }
    });
    return Object.keys(data).length > 0 ? data : null;
};
const auditLogger = async (req, res, next) => {
    if (!AUDITABLE_ACTIONS.includes(req.method) ||
        !AUDITABLE_RESOURCES.some(resource => req.path.startsWith(resource))) {
        next();
        return;
    }
    if (req.path.includes('/health') || req.path.includes('/docs')) {
        next();
        return;
    }
    const startTime = Date.now();
    const originalSend = res.send;
    let responseData = null;
    res.send = function (data) {
        responseData = data;
        return originalSend.call(this, data);
    };
    next();
    res.on('finish', async () => {
        try {
            if (!req.user)
                return;
            const resourceInfo = extractResourceInfo(req.path, req.method);
            if (!resourceInfo)
                return;
            const duration = Date.now() - startTime;
            const requestData = captureRequestData(req);
            let parsedResponseData = null;
            if (responseData && typeof responseData === 'string') {
                try {
                    parsedResponseData = JSON.parse(responseData);
                }
                catch {
                    parsedResponseData = { message: responseData };
                }
            }
            else if (responseData) {
                parsedResponseData = responseData;
            }
            await prisma.auditLog.create({
                data: {
                    userId: req.user.id,
                    action: resourceInfo.action,
                    resource: resourceInfo.resource,
                    resourceId: resourceInfo.resourceId,
                    oldValues: req.method === 'PUT' || req.method === 'PATCH' ? requestData : null,
                    newValues: res.statusCode < 400 ? parsedResponseData : null,
                    ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
                    userAgent: req.get('User-Agent') || 'unknown',
                },
            });
            logger_1.logger.info('Audit Log', {
                userId: req.user.id,
                username: req.user.username,
                action: resourceInfo.action,
                resource: resourceInfo.resource,
                resourceId: resourceInfo.resourceId,
                method: req.method,
                path: req.path,
                statusCode: res.statusCode,
                duration,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
            });
        }
        catch (error) {
            logger_1.logger.error('Audit logging failed:', error);
        }
    });
};
exports.auditLogger = auditLogger;
const createAuditLog = async (userId, action, resource, resourceId, oldValues, newValues, ipAddress, userAgent) => {
    try {
        await prisma.auditLog.create({
            data: {
                userId,
                action,
                resource,
                resourceId: resourceId || null,
                oldValues,
                newValues,
                ipAddress: ipAddress || 'system',
                userAgent: userAgent || 'system',
            },
        });
    }
    catch (error) {
        logger_1.logger.error('Manual audit logging failed:', error);
    }
};
exports.createAuditLog = createAuditLog;
//# sourceMappingURL=auditLogger.js.map