"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = void 0;
const notFoundHandler = (req, res, _next) => {
    res.status(404).json({
        error: 'Endpoint not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
        timestamp: new Date().toISOString(),
        availableEndpoints: {
            auth: '/api/auth',
            dashboard: '/api/dashboard',
            assets: '/api/assets',
            inventory: '/api/inventory',
            purchases: '/api/purchases',
            transfers: '/api/transfers',
            assignments: '/api/assignments',
            users: '/api/users',
            bases: '/api/bases',
            audit: '/api/audit',
        },
    });
};
exports.notFoundHandler = notFoundHandler;
//# sourceMappingURL=notFoundHandler.js.map