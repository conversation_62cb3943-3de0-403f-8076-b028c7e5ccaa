import { UserRoleType, EquipmentCategoryType, TransactionTypeType, TransferStatusType } from './enums';

// Common API response types
export interface ApiResponse<T = any> {
  message?: string;
  data?: T;
  error?: string;
  details?: any;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface DateRangeParams {
  startDate?: string;
  endDate?: string;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  user: UserProfile;
  token: string;
  refreshToken: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  password: string;
  role: UserRoleType;
  baseId?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// User types
export interface UserProfile {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: UserRoleType;
  baseId?: string;
  base?: BaseInfo;
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
}

export interface BaseInfo {
  id: string;
  name: string;
  code: string;
  location?: string;
}

// Asset types
export interface AssetInfo {
  id: string;
  name: string;
  code: string;
  category: EquipmentCategoryType;
  description?: string;
  unitPrice?: number;
  isActive: boolean;
  createdAt: Date;
}

export interface CreateAssetRequest {
  name: string;
  code: string;
  category: EquipmentCategoryType;
  description?: string;
  unitPrice?: number;
}

export interface UpdateAssetRequest {
  name?: string;
  description?: string;
  unitPrice?: number;
  isActive?: boolean;
}

// Inventory types
export interface InventoryInfo {
  id: string;
  baseId: string;
  assetId: string;
  openingBalance: number;
  currentBalance: number;
  assignedCount: number;
  availableCount: number;
  base: BaseInfo;
  asset: AssetInfo;
  updatedAt: Date;
}

// Purchase types
export interface PurchaseItem {
  assetId: string;
  quantity: number;
  unitPrice: number;
}

export interface CreatePurchaseRequest {
  baseId: string;
  vendor: string;
  description?: string;
  items: PurchaseItem[];
}

export interface PurchaseInfo {
  id: string;
  baseId: string;
  vendor: string;
  description?: string;
  totalValue: number;
  createdById: string;
  createdAt: Date;
  base: BaseInfo;
  createdBy: UserProfile;
  items: PurchaseItemInfo[];
}

export interface PurchaseItemInfo {
  id: string;
  purchaseId: string;
  assetId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  asset: AssetInfo;
}

// Transfer types
export interface TransferItem {
  assetId: string;
  quantity: number;
}

export interface CreateTransferRequest {
  fromBaseId: string;
  toBaseId: string;
  description?: string;
  items: TransferItem[];
}

export interface UpdateTransferStatusRequest {
  status: TransferStatusType;
}

export interface TransferInfo {
  id: string;
  fromBaseId: string;
  toBaseId: string;
  status: TransferStatusType;
  description?: string;
  requestedById: string;
  approvedById?: string;
  requestedAt: Date;
  approvedAt?: Date;
  completedAt?: Date;
  fromBase: BaseInfo;
  toBase: BaseInfo;
  requestedBy: UserProfile;
  approvedBy?: UserProfile;
  items: TransferItemInfo[];
}

export interface TransferItemInfo {
  id: string;
  transferId: string;
  assetId: string;
  quantity: number;
  asset: AssetInfo;
}

// Assignment types
export interface CreateAssignmentRequest {
  baseId: string;
  assetId: string;
  assignedTo: string;
  quantity: number;
  description?: string;
}

export interface ReturnAssignmentRequest {
  quantity?: number;
  description?: string;
}

export interface AssignmentInfo {
  id: string;
  baseId: string;
  assetId: string;
  assignedTo: string;
  quantity: number;
  returnedQuantity?: number;
  description?: string;
  assignedAt: Date;
  returnedAt?: Date;
  assignedById: string;
  base: BaseInfo;
  asset: AssetInfo;
  assignedBy: UserProfile;
}

// Dashboard types
export interface DashboardOverview {
  openingBalance: number;
  currentBalance: number;
  assignedCount: number;
  availableCount: number;
  netMovement: number;
  purchases: number;
  transfersIn: number;
  transfersOut: number;
  assignments: number;
  expenditures: number;
}

export interface DashboardMetrics {
  categoryMetrics: CategoryMetric[];
  recentTransactions: TransactionInfo[];
}

export interface CategoryMetric {
  category: EquipmentCategoryType;
  currentBalance: number;
  assignedCount: number;
  availableCount: number;
}

export interface NetMovementDetails {
  purchases: {
    count: number;
    totalQuantity: number;
    totalValue: number;
    transactions: TransactionInfo[];
  };
  transfersIn: {
    count: number;
    totalQuantity: number;
    transactions: TransactionInfo[];
  };
  transfersOut: {
    count: number;
    totalQuantity: number;
    transactions: TransactionInfo[];
  };
}

export interface InventoryAlert {
  lowStock: InventoryInfo[];
  overdueAssignments: AssignmentInfo[];
}

// Transaction types
export interface TransactionInfo {
  id: string;
  type: TransactionTypeType;
  assetId: string;
  baseId: string;
  quantity: number;
  totalValue?: number;
  referenceId?: string;
  description?: string;
  createdById: string;
  createdAt: Date;
  asset: AssetInfo;
  base: BaseInfo;
  createdBy: UserProfile;
}

// Audit types
export interface AuditLogInfo {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  user: UserProfile;
}

// Query parameter types
export interface BaseFilterParams {
  baseId?: string;
}

export interface AssetFilterParams {
  category?: EquipmentCategoryType;
  assetId?: string;
}

export interface ListParams extends PaginationParams, DateRangeParams, BaseFilterParams {
}

export interface AssetListParams extends ListParams, AssetFilterParams {
}
