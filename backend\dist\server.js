"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
console.log('Loading server dependencies...');
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
console.log('Dependencies loaded successfully');
console.log('Loading routes...');
const auth_1 = __importDefault(require("./routes/auth"));
const dashboard_1 = __importDefault(require("./routes/dashboard"));
const assets_1 = __importDefault(require("./routes/assets"));
const inventory_1 = __importDefault(require("./routes/inventory"));
const purchases_1 = __importDefault(require("./routes/purchases"));
const transfers_1 = __importDefault(require("./routes/transfers"));
const assignments_1 = __importDefault(require("./routes/assignments"));
const expenditures_1 = __importDefault(require("./routes/expenditures"));
const users_1 = __importDefault(require("./routes/users"));
const bases_1 = __importDefault(require("./routes/bases"));
const audit_1 = __importDefault(require("./routes/audit"));
console.log('Routes loaded successfully');
console.log('Loading environment variables...');
dotenv_1.default.config();
console.log('Environment variables loaded');
console.log('Initializing Express app...');
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
console.log(`Port configured: ${PORT}`);
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false,
}));
const limiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    message: {
        error: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
app.use((0, cors_1.default)({
    origin: process.env.NODE_ENV === 'production'
        ? process.env.FRONTEND_URL
        : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
    });
});
app.use('/api/auth', auth_1.default);
app.use('/api/dashboard', dashboard_1.default);
app.use('/api/assets', assets_1.default);
app.use('/api/inventory', inventory_1.default);
app.use('/api/purchases', purchases_1.default);
app.use('/api/transfers', transfers_1.default);
app.use('/api/assignments', assignments_1.default);
app.use('/api/expenditures', expenditures_1.default);
app.use('/api/users', users_1.default);
app.use('/api/bases', bases_1.default);
app.use('/api/audit', audit_1.default);
app.get('/api', (_req, res) => {
    res.json({
        name: 'Military Asset Management System API',
        version: '1.0.0',
        description: 'RESTful API for managing military assets across multiple bases',
        endpoints: {
            auth: '/api/auth',
            dashboard: '/api/dashboard',
            assets: '/api/assets',
            inventory: '/api/inventory',
            purchases: '/api/purchases',
            transfers: '/api/transfers',
            assignments: '/api/assignments',
            expenditures: '/api/expenditures',
            users: '/api/users',
            bases: '/api/bases',
            audit: '/api/audit',
        },
        documentation: '/api/docs',
    });
});
console.log('Starting server...');
const server = app.listen(PORT, () => {
    console.log(`🚀 Military Asset Management API server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
    console.log(`📚 API docs: http://localhost:${PORT}/api`);
});
server.on('error', (error) => {
    console.error('Server error:', error);
});
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map