"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const enums_1 = require("../types/enums");
const assignmentController_1 = require("../controllers/assignmentController");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.baseFilter).concat(validation_2.querySchemas.dateRange)), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(assignmentController_1.assignmentController.getAll));
router.get('/:id', (0, auth_1.authorizeResourceOwnership)('assignment'), (0, errorHandler_1.asyncHandler)(assignmentController_1.assignmentController.getById));
router.post('/', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validate)(validation_2.assignmentSchemas.create), auth_1.authorizeBase, (0, errorHandler_1.asyncHandler)(assignmentController_1.assignmentController.create));
router.put('/:id/return', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validate)(validation_2.assignmentSchemas.return), (0, auth_1.authorizeResourceOwnership)('assignment'), (0, errorHandler_1.asyncHandler)(assignmentController_1.assignmentController.returnAsset));
router.put('/:id', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, auth_1.authorizeResourceOwnership)('assignment'), (0, errorHandler_1.asyncHandler)(assignmentController_1.assignmentController.update));
exports.default = router;
//# sourceMappingURL=assignments.js.map