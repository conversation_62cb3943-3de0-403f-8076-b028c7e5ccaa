"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const enums_1 = require("../types/enums");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, validation_1.validateQuery)(validation_2.querySchemas.pagination), (0, errorHandler_1.asyncHandler)(async (_req, res) => {
    res.json({ message: 'Base listing endpoint - TODO' });
}));
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Get base by ID endpoint - TODO' });
}));
router.post('/', (0, auth_1.authorize)([enums_1.UserRole.ADMIN]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Create base endpoint - TODO' });
}));
router.put('/:id', (0, auth_1.authorize)([enums_1.UserRole.ADMIN]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Update base endpoint - TODO' });
}));
exports.default = router;
//# sourceMappingURL=bases.js.map