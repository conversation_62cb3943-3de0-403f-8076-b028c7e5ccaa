"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenditureController = void 0;
const client_1 = require("@prisma/client");
const enums_1 = require("../types/enums");
const prisma = new client_1.PrismaClient();
exports.expenditureController = {
    async getAll(req, res) {
        try {
            const { page = 1, limit = 10, baseId, assetId, type, startDate, endDate, } = req.query;
            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);
            let whereClause = {
                type: {
                    in: ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL']
                }
            };
            if (req.user?.role === enums_1.UserRole.BASE_COMMANDER || req.user?.role === enums_1.UserRole.LOGISTICS_OFFICER) {
                whereClause.baseId = req.user.baseId;
            }
            if (baseId)
                whereClause.baseId = baseId;
            if (assetId)
                whereClause.assetId = assetId;
            if (type)
                whereClause.type = type;
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate)
                    whereClause.createdAt.gte = new Date(startDate);
                if (endDate)
                    whereClause.createdAt.lte = new Date(endDate);
            }
            const [expenditures, total] = await Promise.all([
                prisma.transaction.findMany({
                    where: whereClause,
                    include: {
                        asset: {
                            select: { id: true, name: true, code: true, category: true }
                        },
                        createdBy: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        },
                        approvedBy: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    skip,
                    take,
                }),
                prisma.transaction.count({ where: whereClause }),
            ]);
            const totalPages = Math.ceil(total / take);
            res.json({
                expenditures,
                pagination: {
                    page: Number(page),
                    limit: take,
                    total,
                    pages: totalPages,
                },
            });
        }
        catch (error) {
            console.error('Error fetching expenditures:', error);
            res.status(500).json({ error: 'Failed to fetch expenditures' });
        }
    },
    async getById(req, res) {
        try {
            const { id } = req.params;
            const expenditure = await prisma.transaction.findUnique({
                where: { id },
                include: {
                    asset: {
                        select: { id: true, name: true, code: true, category: true, description: true }
                    },
                    createdBy: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    },
                    approvedBy: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    }
                },
            });
            if (!expenditure) {
                return res.status(404).json({ error: 'Expenditure not found' });
            }
            const expenditureTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
            if (!expenditureTypes.includes(expenditure.type)) {
                return res.status(404).json({ error: 'Transaction is not an expenditure' });
            }
            if (req.user?.role !== enums_1.UserRole.ADMIN && expenditure.baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Access denied' });
            }
            res.json({ expenditure });
        }
        catch (error) {
            console.error('Error fetching expenditure:', error);
            res.status(500).json({ error: 'Failed to fetch expenditure' });
        }
    },
    async create(req, res) {
        try {
            const { baseId, assetId, quantity, type, unitPrice, description, reason } = req.body;
            if (req.user?.role !== enums_1.UserRole.ADMIN && baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Cannot create expenditures for other bases' });
            }
            const validTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
            if (!validTypes.includes(type)) {
                return res.status(400).json({ error: 'Invalid expenditure type' });
            }
            const inventory = await prisma.inventory.findUnique({
                where: {
                    baseId_assetId: {
                        baseId,
                        assetId,
                    }
                },
                include: {
                    asset: true
                }
            });
            if (!inventory) {
                return res.status(400).json({ error: 'Asset not available at this base' });
            }
            if (inventory.availableCount < quantity) {
                return res.status(400).json({
                    error: `Insufficient inventory. Available: ${inventory.availableCount}, Requested: ${quantity}`
                });
            }
            const totalValue = unitPrice ? unitPrice * quantity : null;
            const result = await prisma.$transaction(async (tx) => {
                const expenditure = await tx.transaction.create({
                    data: {
                        type,
                        baseId,
                        assetId,
                        quantity: -quantity,
                        unitPrice,
                        totalValue,
                        description: description || reason,
                        createdById: req.user.id,
                        approvedById: req.user.id,
                        approvedAt: new Date(),
                    },
                    include: {
                        asset: {
                            select: { id: true, name: true, code: true, category: true }
                        },
                        createdBy: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        },
                        approvedBy: {
                            select: { id: true, firstName: true, lastName: true, email: true }
                        }
                    },
                });
                await tx.inventory.update({
                    where: {
                        baseId_assetId: {
                            baseId,
                            assetId,
                        }
                    },
                    data: {
                        availableCount: {
                            decrement: quantity
                        }
                    }
                });
                await tx.auditLog.create({
                    data: {
                        userId: req.user.id,
                        action: 'CREATE',
                        resource: 'EXPENDITURE',
                        resourceId: expenditure.id,
                        newValues: JSON.stringify({
                            type,
                            baseId,
                            assetId,
                            quantity,
                            unitPrice,
                            totalValue,
                            description,
                            reason,
                        }),
                    }
                });
                return expenditure;
            });
            res.status(201).json({ expenditure: result });
        }
        catch (error) {
            console.error('Error creating expenditure:', error);
            res.status(500).json({ error: 'Failed to create expenditure' });
        }
    },
    async update(req, res) {
        try {
            const { id } = req.params;
            const { description, unitPrice } = req.body;
            const expenditure = await prisma.transaction.findUnique({
                where: { id }
            });
            if (!expenditure) {
                return res.status(404).json({ error: 'Expenditure not found' });
            }
            const expenditureTypes = ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL'];
            if (!expenditureTypes.includes(expenditure.type)) {
                return res.status(404).json({ error: 'Transaction is not an expenditure' });
            }
            if (req.user?.role !== enums_1.UserRole.ADMIN && expenditure.baseId !== req.user?.baseId) {
                return res.status(403).json({ error: 'Access denied' });
            }
            const totalValue = unitPrice ? unitPrice * Math.abs(expenditure.quantity) : expenditure.totalValue;
            const updatedExpenditure = await prisma.transaction.update({
                where: { id },
                data: {
                    ...(description && { description }),
                    ...(unitPrice && { unitPrice, totalValue }),
                },
                include: {
                    asset: {
                        select: { id: true, name: true, code: true, category: true }
                    },
                    createdBy: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    },
                    approvedBy: {
                        select: { id: true, firstName: true, lastName: true, email: true }
                    }
                },
            });
            await prisma.auditLog.create({
                data: {
                    userId: req.user.id,
                    action: 'UPDATE',
                    resource: 'EXPENDITURE',
                    resourceId: id,
                    oldValues: JSON.stringify({
                        description: expenditure.description,
                        unitPrice: expenditure.unitPrice,
                        totalValue: expenditure.totalValue,
                    }),
                    newValues: JSON.stringify({ description, unitPrice, totalValue }),
                }
            });
            res.json({ expenditure: updatedExpenditure });
        }
        catch (error) {
            console.error('Error updating expenditure:', error);
            res.status(500).json({ error: 'Failed to update expenditure' });
        }
    },
    async getSummary(req, res) {
        try {
            const { baseId, startDate, endDate } = req.query;
            let whereClause = {
                type: {
                    in: ['EXPENDITURE', 'CONSUMPTION', 'LOSS', 'DAMAGE', 'DISPOSAL']
                }
            };
            if (req.user?.role === enums_1.UserRole.BASE_COMMANDER || req.user?.role === enums_1.UserRole.LOGISTICS_OFFICER) {
                whereClause.baseId = req.user.baseId;
            }
            if (baseId)
                whereClause.baseId = baseId;
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate)
                    whereClause.createdAt.gte = new Date(startDate);
                if (endDate)
                    whereClause.createdAt.lte = new Date(endDate);
            }
            const [totalExpenditures, totalValue, expendituresByType] = await Promise.all([
                prisma.transaction.count({ where: whereClause }),
                prisma.transaction.aggregate({
                    where: whereClause,
                    _sum: { totalValue: true }
                }),
                prisma.transaction.groupBy({
                    by: ['type'],
                    where: whereClause,
                    _count: { id: true },
                    _sum: { quantity: true, totalValue: true }
                })
            ]);
            res.json({
                summary: {
                    totalExpenditures,
                    totalValue: totalValue._sum.totalValue || 0,
                    expendituresByType: expendituresByType.map(item => ({
                        type: item.type,
                        count: item._count.id,
                        totalQuantity: Math.abs(item._sum.quantity || 0),
                        totalValue: item._sum.totalValue || 0,
                    }))
                }
            });
        }
        catch (error) {
            console.error('Error fetching expenditure summary:', error);
            res.status(500).json({ error: 'Failed to fetch expenditure summary' });
        }
    },
};
//# sourceMappingURL=expenditureController.js.map