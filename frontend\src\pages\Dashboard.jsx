import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { dashboardAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { 
  Package, 
  ShoppingCart, 
  ArrowRightLeft, 
  TrendingDown, 
  AlertTriangle,
  Eye,
  X
} from 'lucide-react';

const Dashboard = () => {
  const { user } = useAuth();
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [showNetMovementModal, setShowNetMovementModal] = useState(false);
  const [filters, setFilters] = useState({
    baseId: user?.baseId || '',
    startDate: '',
    endDate: '',
    equipmentType: ''
  });

  // Get dashboard metrics
  const { data: metricsData, isLoading: metricsLoading, error: metricsError } = useQuery({
    queryKey: ['dashboard-metrics', filters],
    queryFn: () => dashboardAPI.getMetrics(filters),
    select: (response) => response.data,
  });

  // Get recent activities
  const { data: activitiesData, isLoading: activitiesLoading } = useQuery({
    queryKey: ['dashboard-activities', filters],
    queryFn: () => dashboardAPI.getRecentActivities({ ...filters, limit: 10 }),
    select: (response) => response.data,
  });

  // Get inventory alerts
  const { data: alertsData, isLoading: alertsLoading } = useQuery({
    queryKey: ['dashboard-alerts', filters],
    queryFn: () => dashboardAPI.getInventoryAlerts(filters),
    select: (response) => response.data,
  });

  // Get net movement details for modal
  const { data: netMovementData, isLoading: netMovementLoading } = useQuery({
    queryKey: ['net-movement', user?.baseId, filters],
    queryFn: () => dashboardAPI.getNetMovementDetails(user?.baseId, filters),
    select: (response) => response.data,
    enabled: showNetMovementModal && !!user?.baseId,
  });

  if (metricsLoading) return <LoadingSpinner text="Loading dashboard..." />;
  if (metricsError) return <div style={{color: 'red'}}>Error loading dashboard: {metricsError.message}</div>;

  // Calculate metrics from the API data
  const totalAssets = metricsData?.summary?.totalAssets || 0;
  const openingBalance = metricsData?.summary?.openingBalance || 0;
  const closingBalance = metricsData?.summary?.closingBalance || 0;
  const netMovement = metricsData?.summary?.netMovement || 0;
  const assignedCount = metricsData?.summary?.assignedCount || 0;
  const expendedCount = metricsData?.summary?.expendedCount || 0;

  const metrics = [
    {
      name: 'Opening Balance',
      value: openingBalance,
      icon: Package,
      color: 'bg-blue-500',
      description: 'Starting inventory count'
    },
    {
      name: 'Closing Balance',
      value: closingBalance,
      icon: Package,
      color: 'bg-green-500',
      description: 'Current inventory count'
    },
    {
      name: 'Net Movement',
      value: netMovement,
      icon: ArrowRightLeft,
      color: netMovement >= 0 ? 'bg-green-500' : 'bg-red-500',
      description: 'Purchases + Transfers In - Transfers Out',
      clickable: true
    },
    {
      name: 'Assigned',
      value: assignedCount,
      icon: TrendingDown,
      color: 'bg-yellow-500',
      description: 'Assets assigned to personnel'
    },
    {
      name: 'Expended',
      value: expendedCount,
      icon: TrendingDown,
      color: 'bg-red-500',
      description: 'Assets marked as expended'
    },
  ];

  const recentTransactions = activitiesData?.activities || [];
  const lowStockAlerts = alertsData?.alerts?.lowStock || [];

  const NetMovementModal = () => {
    if (netMovementLoading) {
      return (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <LoadingSpinner text="Loading net movement details..." />
          </div>
        </div>
      );
    }

    const purchases = netMovementData?.purchases || {};
    const transfersIn = netMovementData?.transfersIn || {};
    const transfersOut = netMovementData?.transfersOut || {};

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        overflowY: 'auto',
        zIndex: 50
      }}>
        <div style={{
          position: 'relative',
          top: '80px',
          margin: '0 auto',
          padding: '20px',
          border: '1px solid #ccc',
          width: '90%',
          maxWidth: '800px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#111827' }}>Net Movement Details</h3>
            <button
              onClick={() => setShowNetMovementModal(false)}
              style={{
                color: '#9CA3AF',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '20px'
              }}
            >
              <X style={{ width: '24px', height: '24px' }} />
            </button>
          </div>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px', marginBottom: '20px' }}>
            <div style={{ backgroundColor: '#F0FDF4', padding: '16px', borderRadius: '8px' }}>
              <h4 style={{ fontWeight: '500', color: '#166534', marginBottom: '8px' }}>Purchases</h4>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#16A34A', margin: '0' }}>
                +{purchases.totalQuantity || 0}
              </p>
              <p style={{ fontSize: '12px', color: '#16A34A', margin: '4px 0 0 0' }}>
                {purchases.count || 0} transactions
              </p>
              <p style={{ fontSize: '12px', color: '#16A34A', margin: '2px 0 0 0' }}>
                ${(purchases.totalValue || 0).toLocaleString()}
              </p>
            </div>
            <div style={{ backgroundColor: '#EFF6FF', padding: '16px', borderRadius: '8px' }}>
              <h4 style={{ fontWeight: '500', color: '#1E40AF', marginBottom: '8px' }}>Transfers In</h4>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563EB', margin: '0' }}>
                +{transfersIn.totalQuantity || 0}
              </p>
              <p style={{ fontSize: '12px', color: '#2563EB', margin: '4px 0 0 0' }}>
                {transfersIn.count || 0} transactions
              </p>
            </div>
            <div style={{ backgroundColor: '#FEF2F2', padding: '16px', borderRadius: '8px' }}>
              <h4 style={{ fontWeight: '500', color: '#991B1B', marginBottom: '8px' }}>Transfers Out</h4>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#DC2626', margin: '0' }}>
                -{transfersOut.totalQuantity || 0}
              </p>
              <p style={{ fontSize: '12px', color: '#DC2626', margin: '4px 0 0 0' }}>
                {transfersOut.count || 0} transactions
              </p>
            </div>
          </div>
          <div style={{ backgroundColor: '#F3F4F6', padding: '16px', borderRadius: '8px' }}>
            <h4 style={{ fontWeight: '500', color: '#374151', marginBottom: '8px' }}>Net Movement</h4>
            <p style={{ fontSize: '32px', fontWeight: 'bold', color: '#1F2937', margin: '0' }}>
              {((purchases.totalQuantity || 0) + (transfersIn.totalQuantity || 0) - (transfersOut.totalQuantity || 0)) >= 0 ? '+' : ''}
              {(purchases.totalQuantity || 0) + (transfersIn.totalQuantity || 0) - (transfersOut.totalQuantity || 0)}
            </p>
            <p style={{ fontSize: '12px', color: '#6B7280', margin: '4px 0 0 0' }}>
              Total net change in inventory
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {/* Header */}
      <div>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: '0 0 4px 0' }}>
          Military Asset Dashboard
        </h1>
        <p style={{ fontSize: '14px', color: '#6B7280', margin: '0' }}>
          Welcome back, {user?.firstName} {user?.lastName}. Here's your asset overview.
        </p>
      </div>

      {/* Filters */}
      <div style={{
        backgroundColor: 'white',
        padding: '16px',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px'
      }}>
        <div>
          <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
            Start Date
          </label>
          <input
            type="date"
            value={filters.startDate}
            onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>
        <div>
          <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
            End Date
          </label>
          <input
            type="date"
            value={filters.endDate}
            onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
        </div>
        <div>
          <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
            Equipment Type
          </label>
          <select
            value={filters.equipmentType}
            onChange={(e) => setFilters(prev => ({ ...prev, equipmentType: e.target.value }))}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #D1D5DB',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            <option value="">All Types</option>
            <option value="WEAPON">Weapons</option>
            <option value="VEHICLE">Vehicles</option>
            <option value="AMMUNITION">Ammunition</option>
            <option value="EQUIPMENT">Equipment</option>
            <option value="SUPPLIES">Supplies</option>
          </select>
        </div>
      </div>

      {/* Metrics Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '20px'
      }}>
        {metrics.map((metric) => (
          <div
            key={metric.name}
            style={{
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              cursor: metric.clickable ? 'pointer' : 'default',
              transition: 'transform 0.2s, box-shadow 0.2s'
            }}
            onClick={metric.clickable ? () => setShowNetMovementModal(true) : undefined}
            onMouseEnter={(e) => {
              if (metric.clickable) {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
              }
            }}
            onMouseLeave={(e) => {
              if (metric.clickable) {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
              }
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{ flexShrink: 0 }}>
                <div style={{
                  backgroundColor: metric.color.includes('blue') ? '#3B82F6' :
                                 metric.color.includes('green') ? '#10B981' :
                                 metric.color.includes('yellow') ? '#F59E0B' :
                                 metric.color.includes('red') ? '#EF4444' : '#6B7280',
                  padding: '12px',
                  borderRadius: '6px'
                }}>
                  <metric.icon style={{ width: '24px', height: '24px', color: 'white' }} />
                </div>
              </div>
              <div style={{ marginLeft: '20px', flex: 1 }}>
                <div style={{ fontSize: '12px', fontWeight: '500', color: '#6B7280', marginBottom: '4px' }}>
                  {metric.name}
                </div>
                <div style={{ fontSize: '28px', fontWeight: '600', color: '#111827', marginBottom: '2px' }}>
                  {metric.value.toLocaleString()}
                </div>
                <div style={{ fontSize: '11px', color: '#9CA3AF' }}>
                  {metric.description}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
        {/* Recent Transactions */}
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ fontSize: '18px', fontWeight: '500', color: '#111827', marginBottom: '16px' }}>
            Recent Activities
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {activitiesLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <LoadingSpinner text="Loading activities..." />
              </div>
            ) : recentTransactions.length > 0 ? (
              recentTransactions.map((transaction, index) => (
                <div key={transaction.id || index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingBottom: '12px',
                  borderBottom: index < recentTransactions.length - 1 ? '1px solid #F3F4F6' : 'none'
                }}>
                  <div>
                    <p style={{ fontSize: '14px', fontWeight: '500', color: '#111827', margin: '0 0 2px 0' }}>
                      {transaction.type?.replace('_', ' ')}
                    </p>
                    <p style={{ fontSize: '12px', color: '#6B7280', margin: '0' }}>
                      {transaction.asset?.name} - {transaction.quantity} units
                    </p>
                    <p style={{ fontSize: '11px', color: '#9CA3AF', margin: '2px 0 0 0' }}>
                      by {transaction.createdBy?.firstName} {transaction.createdBy?.lastName}
                    </p>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <p style={{ fontSize: '12px', color: '#111827', margin: '0 0 2px 0' }}>
                      {new Date(transaction.createdAt).toLocaleDateString()}
                    </p>
                    <p style={{
                      fontSize: '11px',
                      color: transaction.status === 'COMPLETED' ? '#059669' :
                             transaction.status === 'PENDING' ? '#D97706' : '#DC2626',
                      margin: '0'
                    }}>
                      {transaction.status || 'COMPLETED'}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p style={{ fontSize: '14px', color: '#6B7280', textAlign: 'center', padding: '20px' }}>
                No recent activities
              </p>
            )}
          </div>
        </div>

        {/* Low Stock Alerts */}
        <div style={{
          backgroundColor: 'white',
          padding: '24px',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{
            fontSize: '18px',
            fontWeight: '500',
            color: '#111827',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <AlertTriangle style={{ width: '20px', height: '20px', color: '#F59E0B', marginRight: '8px' }} />
            Low Stock Alerts
          </h2>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            {alertsLoading ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <LoadingSpinner text="Loading alerts..." />
              </div>
            ) : lowStockAlerts.length > 0 ? (
              lowStockAlerts.map((alert, index) => (
                <div key={alert.id || index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingBottom: '12px',
                  borderBottom: index < lowStockAlerts.length - 1 ? '1px solid #F3F4F6' : 'none'
                }}>
                  <div>
                    <p style={{ fontSize: '14px', fontWeight: '500', color: '#111827', margin: '0 0 2px 0' }}>
                      {alert.asset?.name}
                    </p>
                    <p style={{ fontSize: '12px', color: '#6B7280', margin: '0' }}>
                      {alert.base?.name} ({alert.base?.code})
                    </p>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <p style={{ fontSize: '14px', fontWeight: 'bold', color: '#DC2626', margin: '0 0 2px 0' }}>
                      {alert.availableCount}
                    </p>
                    <p style={{ fontSize: '11px', color: '#6B7280', margin: '0' }}>
                      Available
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p style={{ fontSize: '14px', color: '#6B7280', textAlign: 'center', padding: '20px' }}>
                No low stock alerts
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Net Movement Modal */}
      {showNetMovementModal && <NetMovementModal />}
    </div>
  );
};

export default Dashboard;
