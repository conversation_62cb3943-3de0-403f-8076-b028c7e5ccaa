import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { reportsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { 
  Package, 
  ShoppingCart, 
  ArrowRightLeft, 
  TrendingDown, 
  AlertTriangle,
  Eye,
  X
} from 'lucide-react';

const Dashboard = () => {
  const { user } = useAuth();
  const [selectedMetric, setSelectedMetric] = useState(null);
  const [showNetMovementModal, setShowNetMovementModal] = useState(false);

  const { data: dashboardData, isLoading, error } = useQuery({
    queryKey: ['dashboard'],
    queryFn: () => reportsAPI.getDashboard(),
    select: (response) => response.data,
  });

  if (isLoading) return <LoadingSpinner text="Loading dashboard..." />;
  if (error) return <div className="text-red-600">Error loading dashboard: {error.message}</div>;

  const metrics = [
    {
      name: 'Total Assets',
      value: dashboardData?.totalAssets || 0,
      icon: Package,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      name: 'Pending Purchases',
      value: dashboardData?.pendingPurchases || 0,
      icon: ShoppingCart,
      color: 'bg-green-500',
      change: '+5%',
      changeType: 'increase'
    },
    {
      name: 'Active Transfers',
      value: dashboardData?.activeTransfers || 0,
      icon: ArrowRightLeft,
      color: 'bg-yellow-500',
      change: '-2%',
      changeType: 'decrease'
    },
    {
      name: 'Monthly Expenditures',
      value: dashboardData?.monthlyExpenditures || 0,
      icon: TrendingDown,
      color: 'bg-red-500',
      change: '+8%',
      changeType: 'increase'
    },
  ];

  const recentTransactions = dashboardData?.recentTransactions || [];
  const lowStockAlerts = dashboardData?.lowStockAlerts || [];

  const NetMovementModal = () => (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Net Movement Details</h3>
          <button
            onClick={() => setShowNetMovementModal(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-800">Inbound</h4>
              <p className="text-2xl font-bold text-green-600">
                +{dashboardData?.netMovement?.inbound || 0}
              </p>
              <p className="text-sm text-green-600">Purchases + Transfers In</p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-medium text-red-800">Outbound</h4>
              <p className="text-2xl font-bold text-red-600">
                -{dashboardData?.netMovement?.outbound || 0}
              </p>
              <p className="text-sm text-red-600">Assignments + Expenditures + Transfers Out</p>
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800">Net Movement</h4>
            <p className="text-3xl font-bold text-blue-600">
              {((dashboardData?.netMovement?.inbound || 0) - (dashboardData?.netMovement?.outbound || 0)) >= 0 ? '+' : ''}
              {(dashboardData?.netMovement?.inbound || 0) - (dashboardData?.netMovement?.outbound || 0)}
            </p>
            <p className="text-sm text-blue-600">Total net change in inventory</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back, {user?.name}. Here's what's happening with your assets.
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => (
          <div key={metric.name} className="card p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${metric.color} p-3 rounded-md`}>
                  <metric.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {metric.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {metric.value.toLocaleString()}
                    </div>
                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                      metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {metric.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Net Movement Card */}
      <div className="card p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">Net Movement Summary</h2>
          <button
            onClick={() => setShowNetMovementModal(true)}
            className="btn-primary flex items-center"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </button>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              +{dashboardData?.netMovement?.inbound || 0}
            </p>
            <p className="text-sm text-gray-500">Inbound</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-red-600">
              -{dashboardData?.netMovement?.outbound || 0}
            </p>
            <p className="text-sm text-gray-500">Outbound</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {((dashboardData?.netMovement?.inbound || 0) - (dashboardData?.netMovement?.outbound || 0)) >= 0 ? '+' : ''}
              {(dashboardData?.netMovement?.inbound || 0) - (dashboardData?.netMovement?.outbound || 0)}
            </p>
            <p className="text-sm text-gray-500">Net Movement</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Transactions */}
        <div className="card p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Transactions</h2>
          <div className="space-y-3">
            {recentTransactions.length > 0 ? (
              recentTransactions.map((transaction, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{transaction.type}</p>
                    <p className="text-xs text-gray-500">{transaction.asset} - {transaction.quantity} units</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-900">{transaction.date}</p>
                    <p className={`text-xs ${
                      transaction.status === 'COMPLETED' ? 'text-green-600' : 
                      transaction.status === 'PENDING' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {transaction.status}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No recent transactions</p>
            )}
          </div>
        </div>

        {/* Low Stock Alerts */}
        <div className="card p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
            Low Stock Alerts
          </h2>
          <div className="space-y-3">
            {lowStockAlerts.length > 0 ? (
              lowStockAlerts.map((alert, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{alert.asset}</p>
                    <p className="text-xs text-gray-500">{alert.base}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-red-600">{alert.currentStock}</p>
                    <p className="text-xs text-gray-500">Min: {alert.minThreshold}</p>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500">No low stock alerts</p>
            )}
          </div>
        </div>
      </div>

      {/* Net Movement Modal */}
      {showNetMovementModal && <NetMovementModal />}
    </div>
  );
};

export default Dashboard;
