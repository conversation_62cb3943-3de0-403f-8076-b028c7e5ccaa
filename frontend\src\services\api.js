import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  me: () => api.get('/auth/me'),
  refreshToken: () => api.post('/auth/refresh'),
};

// Users API
export const usersAPI = {
  getAll: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  create: (userData) => api.post('/users', userData),
  update: (id, userData) => api.put(`/users/${id}`, userData),
  delete: (id) => api.delete(`/users/${id}`),
};

// Bases API
export const basesAPI = {
  getAll: (params) => api.get('/bases', { params }),
  getById: (id) => api.get(`/bases/${id}`),
  create: (baseData) => api.post('/bases', baseData),
  update: (id, baseData) => api.put(`/bases/${id}`, baseData),
  delete: (id) => api.delete(`/bases/${id}`),
};

// Assets API
export const assetsAPI = {
  getAll: (params) => api.get('/assets', { params }),
  getById: (id) => api.get(`/assets/${id}`),
  create: (assetData) => api.post('/assets', assetData),
  update: (id, assetData) => api.put(`/assets/${id}`, assetData),
  delete: (id) => api.delete(`/assets/${id}`),
  getByBase: (baseId, params) => api.get(`/assets/base/${baseId}`, { params }),
};

// Inventory API
export const inventoryAPI = {
  getAll: (params) => api.get('/inventory', { params }),
  getById: (id) => api.get(`/inventory/${id}`),
  getByBase: (baseId, params) => api.get(`/inventory/base/${baseId}`, { params }),
  getByAsset: (assetId, params) => api.get(`/inventory/asset/${assetId}`, { params }),
  update: (id, inventoryData) => api.put(`/inventory/${id}`, inventoryData),
};

// Transactions API
export const transactionsAPI = {
  getAll: (params) => api.get('/transactions', { params }),
  getById: (id) => api.get(`/transactions/${id}`),
  create: (transactionData) => api.post('/transactions', transactionData),
  update: (id, transactionData) => api.put(`/transactions/${id}`, transactionData),
  delete: (id) => api.delete(`/transactions/${id}`),
  getByBase: (baseId, params) => api.get(`/transactions/base/${baseId}`, { params }),
};

// Purchases API
export const purchasesAPI = {
  getAll: (params) => api.get('/purchases', { params }),
  getById: (id) => api.get(`/purchases/${id}`),
  create: (purchaseData) => api.post('/purchases', purchaseData),
  update: (id, purchaseData) => api.put(`/purchases/${id}`, purchaseData),
  delete: (id) => api.delete(`/purchases/${id}`),
};

// Transfers API
export const transfersAPI = {
  getAll: (params) => api.get('/transfers', { params }),
  getById: (id) => api.get(`/transfers/${id}`),
  create: (transferData) => api.post('/transfers', transferData),
  update: (id, transferData) => api.put(`/transfers/${id}`, transferData),
  delete: (id) => api.delete(`/transfers/${id}`),
  approve: (id) => api.post(`/transfers/${id}/approve`),
  reject: (id, reason) => api.post(`/transfers/${id}/reject`, { reason }),
  complete: (id) => api.post(`/transfers/${id}/complete`),
};

// Assignments API
export const assignmentsAPI = {
  getAll: (params) => api.get('/assignments', { params }),
  getById: (id) => api.get(`/assignments/${id}`),
  create: (assignmentData) => api.post('/assignments', assignmentData),
  update: (id, assignmentData) => api.put(`/assignments/${id}`, assignmentData),
  delete: (id) => api.delete(`/assignments/${id}`),
  return: (id) => api.post(`/assignments/${id}/return`),
};

// Expenditures API
export const expendituresAPI = {
  getAll: (params) => api.get('/expenditures', { params }),
  getById: (id) => api.get(`/expenditures/${id}`),
  create: (expenditureData) => api.post('/expenditures', expenditureData),
  update: (id, expenditureData) => api.put(`/expenditures/${id}`, expenditureData),
  delete: (id) => api.delete(`/expenditures/${id}`),
};

// Dashboard API
export const dashboardAPI = {
  getMetrics: (params) => api.get('/dashboard/metrics', { params }),
  getNetMovementDetails: (baseId, params) => api.get(`/dashboard/net-movement/${baseId}`, { params }),
  getRecentActivities: (params) => api.get('/dashboard/activities', { params }),
  getInventoryAlerts: (params) => api.get('/dashboard/alerts', { params }),
  getChartsData: (params) => api.get('/dashboard/charts', { params }),
};

// Reports API
export const reportsAPI = {
  getDashboard: (params) => api.get('/reports/dashboard', { params }),
  getInventoryReport: (params) => api.get('/reports/inventory', { params }),
  getTransactionReport: (params) => api.get('/reports/transactions', { params }),
  getAuditReport: (params) => api.get('/reports/audit', { params }),
  exportReport: (type, params) => api.get(`/reports/export/${type}`, {
    params,
    responseType: 'blob'
  }),
};

// Audit API
export const auditAPI = {
  getAll: (params) => api.get('/audit', { params }),
  getById: (id) => api.get(`/audit/${id}`),
  getByEntity: (entityType, entityId, params) => 
    api.get(`/audit/${entityType}/${entityId}`, { params }),
};

export default api;
