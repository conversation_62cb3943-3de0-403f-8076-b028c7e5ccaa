"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../utils/validation");
const validation_2 = require("../utils/validation");
const errorHandler_1 = require("../middleware/errorHandler");
const enums_1 = require("../types/enums");
const router = (0, express_1.Router)();
router.use(auth_1.authenticate);
router.get('/', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.dateRange)), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Audit log listing endpoint - TODO' });
}));
router.get('/:id', (0, auth_1.authorize)([enums_1.UserRole.ADMIN]), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Get audit log by ID endpoint - TODO' });
}));
router.get('/user/:userId', (0, auth_1.authorize)([enums_1.UserRole.ADMIN]), (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.dateRange)), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'User audit logs endpoint - TODO' });
}));
router.get('/resource/:resource/:resourceId', (0, auth_1.authorize)([enums_1.UserRole.ADMIN, enums_1.UserRole.BASE_COMMANDER]), (0, validation_1.validateQuery)(validation_2.querySchemas.pagination.concat(validation_2.querySchemas.dateRange)), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({ message: 'Resource audit logs endpoint - TODO' });
}));
exports.default = router;
//# sourceMappingURL=audit.js.map