import { Router } from 'express';
import { authenticate, authorize, authorizeBase, authorizeResourceOwnership } from '../middleware/auth';
import { validate, validateQuery } from '../utils/validation';
import { assignmentSchemas, querySchemas } from '../utils/validation';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { UserRole } from '../types/enums';
import { assignmentController } from '../controllers/assignmentController';

const router = Router();

// All routes require authentication
router.use(authenticate);

// GET /api/assignments - List assignments
router.get(
  '/',
  validateQuery(querySchemas.pagination.concat(querySchemas.baseFilter).concat(querySchemas.dateRange)),
  authorizeBase,
  asyncHandler(assignmentController.getAll)
);

// GET /api/assignments/:id - Get specific assignment
router.get(
  '/:id',
  authorizeResourceOwnership('assignment'),
  as<PERSON><PERSON><PERSON><PERSON>(assignmentController.getById)
);

// POST /api/assignments - Create new assignment
router.post(
  '/',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  validate(assignmentSchemas.create),
  authorizeBase,
  asyncHandler(assignmentController.create)
);

// PUT /api/assignments/:id/return - Return assigned asset
router.put(
  '/:id/return',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  validate(assignmentSchemas.return),
  authorizeResourceOwnership('assignment'),
  asyncHandler(assignmentController.returnAsset)
);

// PUT /api/assignments/:id - Update assignment
router.put(
  '/:id',
  authorize([UserRole.ADMIN, UserRole.BASE_COMMANDER]),
  authorizeResourceOwnership('assignment'),
  asyncHandler(assignmentController.update)
);

export default router;
