import { Request, Response, NextFunction } from 'express';

export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString(),
    availableEndpoints: {
      auth: '/api/auth',
      dashboard: '/api/dashboard',
      assets: '/api/assets',
      inventory: '/api/inventory',
      purchases: '/api/purchases',
      transfers: '/api/transfers',
      assignments: '/api/assignments',
      users: '/api/users',
      bases: '/api/bases',
      audit: '/api/audit',
    },
  });
};
