# Quick System Test
Write-Host "🚀 MILITARY ASSET MANAGEMENT SYSTEM - QUICK TEST" -ForegroundColor Green

# Test Backend Health
try {
    $health = Invoke-WebRequest -Uri "http://localhost:3001/health" -UseBasicParsing
    Write-Host "✅ Backend Health: OK" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend Health: FAILED" -ForegroundColor Red
}

# Test Frontend
try {
    $frontend = Invoke-WebRequest -Uri "http://localhost:5176" -UseBasicParsing
    Write-Host "✅ Frontend: OK" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend: FAILED" -ForegroundColor Red
}

# Test Login
try {
    $loginData = @{
        email = "<EMAIL>"
        password = "admin123"
    } | ConvertTo-Json

    $login = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -UseBasicParsing
    $loginResult = $login.Content | ConvertFrom-Json
    
    if ($loginResult.token) {
        Write-Host "✅ Admin Login: OK" -ForegroundColor Green
        
        # Test protected endpoint
        $headers = @{ "Authorization" = "Bearer $($loginResult.token)" }
        $dashboard = Invoke-WebRequest -Uri "http://localhost:3001/api/dashboard/stats" -Headers $headers -UseBasicParsing
        Write-Host "✅ Protected Endpoint: OK" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Authentication: FAILED" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 SYSTEM STATUS SUMMARY:" -ForegroundColor Cyan
Write-Host "• Backend API: http://localhost:3001" -ForegroundColor White
Write-Host "• Frontend App: http://localhost:5176" -ForegroundColor White
Write-Host "• Ready for manual testing!" -ForegroundColor Green
