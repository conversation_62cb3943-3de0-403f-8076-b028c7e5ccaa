import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuth } from '../contexts/AuthContext';

const schema = yup.object({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().required('Password is required'),
});

const Login = () => {
  const { login, isLoading, error } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    await login(data);
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '48px 16px',
        backgroundImage: `url('https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative'
      }}
    >
      {/* Overlay for better text readability */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)'
      }}></div>

      <div style={{
        position: 'relative',
        zIndex: 10,
        maxWidth: '400px',
        width: '100%'
      }}>
        {/* Title */}
        <h1 style={{
          textAlign: 'center',
          fontSize: '2.5rem',
          fontWeight: 'bold',
          color: 'white',
          marginBottom: '32px',
          letterSpacing: '0.05em'
        }}>
          Military Database
        </h1>
        {/* Login Card */}
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(4px)',
          borderRadius: '8px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          padding: '32px'
        }}>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#374151',
            textAlign: 'center',
            marginBottom: '24px'
          }}>
            Login
          </h2>

          <form style={{ display: 'flex', flexDirection: 'column', gap: '16px' }} onSubmit={handleSubmit(onSubmit)}>
            {error && (
              <div style={{
                borderRadius: '6px',
                backgroundColor: '#fef2f2',
                padding: '16px'
              }}>
                <div style={{ fontSize: '14px', color: '#b91c1c' }}>{error}</div>
              </div>
            )}

            <div>
              <input
                {...register('email')}
                type="email"
                autoComplete="email"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: errors.email ? '1px solid #fca5a5' : '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px',
                  outline: 'none',
                  transition: 'border-color 0.2s, box-shadow 0.2s'
                }}
                placeholder="User name"
                onFocus={(e) => {
                  e.target.style.borderColor = errors.email ? '#ef4444' : '#3b82f6';
                  e.target.style.boxShadow = errors.email ? '0 0 0 3px rgba(239, 68, 68, 0.1)' : '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = errors.email ? '#fca5a5' : '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
              />
              {errors.email && (
                <p style={{ marginTop: '4px', fontSize: '14px', color: '#dc2626' }}>{errors.email.message}</p>
              )}
            </div>

            <div>
              <input
                {...register('password')}
                type="password"
                autoComplete="current-password"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: errors.password ? '1px solid #fca5a5' : '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '16px',
                  outline: 'none',
                  transition: 'border-color 0.2s, box-shadow 0.2s'
                }}
                placeholder="Password"
                onFocus={(e) => {
                  e.target.style.borderColor = errors.password ? '#ef4444' : '#3b82f6';
                  e.target.style.boxShadow = errors.password ? '0 0 0 3px rgba(239, 68, 68, 0.1)' : '0 0 0 3px rgba(59, 130, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = errors.password ? '#fca5a5' : '#d1d5db';
                  e.target.style.boxShadow = 'none';
                }}
              />
              {errors.password && (
                <p style={{ marginTop: '4px', fontSize: '14px', color: '#dc2626' }}>{errors.password.message}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              style={{
                width: '100%',
                backgroundColor: isLoading ? '#9ca3af' : '#16a34a',
                color: 'white',
                fontWeight: '500',
                padding: '12px 16px',
                borderRadius: '6px',
                border: 'none',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s',
                fontSize: '16px'
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.target.style.backgroundColor = '#15803d';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.target.style.backgroundColor = '#16a34a';
                }
              }}
            >
              {isLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    border: '2px solid white',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                    marginRight: '12px'
                  }}></div>
                  Submitting...
                </div>
              ) : (
                'Submit'
              )}
            </button>
          </form>

          {/* Demo credentials */}
          <div style={{ marginTop: '24px', textAlign: 'center' }}>
            <div style={{ fontSize: '14px', color: '#6b7280' }}>
              <p style={{ fontWeight: '500' }}>Demo Accounts:</p>
              <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '12px' }}>
                <p><strong>Admin:</strong> <EMAIL> / password123</p>
                <p><strong>Commander:</strong> <EMAIL> / password123</p>
                <p><strong>Logistics:</strong> <EMAIL> / password123</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS animation for spinner */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Login;
