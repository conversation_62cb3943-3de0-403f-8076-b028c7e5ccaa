import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useAuth } from '../contexts/AuthContext';

const schema = yup.object({
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string().required('Password is required'),
});

const Login = () => {
  const { login, isLoading, error } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    await login(data);
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '48px 16px',
        backgroundImage: `url('https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        position: 'relative'
      }}
    >
      {/* Overlay for better text readability */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.4)'
      }}></div>

      <div style={{
        position: 'relative',
        zIndex: 10,
        maxWidth: '380px',
        width: '100%',
        margin: '0 auto'
      }}>
        {/* Title */}
        <h1 style={{
          textAlign: 'center',
          fontSize: '3rem',
          fontWeight: 'normal',
          color: 'white',
          marginBottom: '40px',
          letterSpacing: '0.1em',
          textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)',
          fontFamily: 'serif'
        }}>
          Military Database
        </h1>
        {/* Login Card */}
        <div style={{
          backgroundColor: 'rgba(240, 240, 240, 0.9)',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          padding: '40px',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          maxWidth: '350px',
          margin: '0 auto'
        }}>
          <h2 style={{
            fontSize: '1.8rem',
            fontWeight: '500',
            color: '#333333',
            textAlign: 'center',
            marginBottom: '30px',
            fontFamily: 'sans-serif'
          }}>
            Login
          </h2>

          <form style={{ display: 'flex', flexDirection: 'column', gap: '20px' }} onSubmit={handleSubmit(onSubmit)}>
            {error && (
              <div style={{
                borderRadius: '4px',
                backgroundColor: '#fef2f2',
                padding: '12px',
                border: '1px solid #fecaca'
              }}>
                <div style={{ fontSize: '14px', color: '#b91c1c' }}>{error}</div>
              </div>
            )}

            <div>
              <input
                {...register('email')}
                type="email"
                autoComplete="email"
                style={{
                  width: '100%',
                  padding: '14px 16px',
                  border: errors.email ? '1px solid #ef4444' : '1px solid #cccccc',
                  borderRadius: '4px',
                  fontSize: '15px',
                  outline: 'none',
                  backgroundColor: 'white',
                  boxSizing: 'border-box',
                  fontFamily: 'sans-serif'
                }}
                placeholder="User name"
                onFocus={(e) => {
                  e.target.style.borderColor = errors.email ? '#ef4444' : '#666666';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = errors.email ? '#ef4444' : '#cccccc';
                }}
              />
              {errors.email && (
                <p style={{ marginTop: '6px', fontSize: '13px', color: '#dc2626' }}>{errors.email.message}</p>
              )}
            </div>

            <div>
              <input
                {...register('password')}
                type="password"
                autoComplete="current-password"
                style={{
                  width: '100%',
                  padding: '14px 16px',
                  border: errors.password ? '1px solid #ef4444' : '1px solid #cccccc',
                  borderRadius: '4px',
                  fontSize: '15px',
                  outline: 'none',
                  backgroundColor: 'white',
                  boxSizing: 'border-box',
                  fontFamily: 'sans-serif'
                }}
                placeholder="Password"
                onFocus={(e) => {
                  e.target.style.borderColor = errors.password ? '#ef4444' : '#666666';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = errors.password ? '#ef4444' : '#cccccc';
                }}
              />
              {errors.password && (
                <p style={{ marginTop: '6px', fontSize: '13px', color: '#dc2626' }}>{errors.password.message}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading}
              style={{
                width: '100%',
                backgroundColor: isLoading ? '#9ca3af' : '#22c55e',
                color: 'white',
                fontWeight: '600',
                padding: '14px 16px',
                borderRadius: '4px',
                border: 'none',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.15s',
                fontSize: '16px',
                fontFamily: 'sans-serif',
                marginTop: '8px'
              }}
              onMouseEnter={(e) => {
                if (!isLoading) {
                  e.target.style.backgroundColor = '#16a34a';
                }
              }}
              onMouseLeave={(e) => {
                if (!isLoading) {
                  e.target.style.backgroundColor = '#22c55e';
                }
              }}
            >
              {isLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <div style={{
                    width: '18px',
                    height: '18px',
                    border: '2px solid white',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                    marginRight: '10px'
                  }}></div>
                  Submitting...
                </div>
              ) : (
                'Submit'
              )}
            </button>
          </form>

          {/* Demo credentials - smaller and less prominent */}
          <div style={{ marginTop: '30px', textAlign: 'center' }}>
            <div style={{ fontSize: '12px', color: '#888888' }}>
              <p style={{ fontWeight: '500', marginBottom: '6px' }}>Demo Accounts:</p>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px', fontSize: '11px' }}>
                <p><strong>Admin:</strong> <EMAIL> / password123</p>
                <p><strong>Commander:</strong> <EMAIL> / password123</p>
                <p><strong>Logistics:</strong> <EMAIL> / password123</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS animation for spinner */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default Login;
